<?php

/**
 * 现代化错误处理类
 * 提供结构化错误处理、日志记录和调试支持
 */
class ModernErrorHandler {
    
    private static $instance = null;
    private $errorLog = [];
    private $isDebugMode = false;
    private $logPath = './log/';
    
    private function __construct() {
        $this->isDebugMode = defined('DEBUG') && DEBUG;
        $this->logPath = G('conf')['log_path'] ?? './log/';
        
        // 确保日志目录存在
        if (!is_dir($this->logPath)) {
            @mkdir($this->logPath, 0755, true);
        }
    }
    
    /**
     * 获取单例实例
     */
    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 注册错误处理器
     */
    public function register(): void {
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleShutdown']);
    }
    
    /**
     * 处理PHP错误
     */
    public function handleError(int $severity, string $message, string $file = '', int $line = 0): bool {
        // 如果错误被@抑制，不处理
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $error = [
            'type' => 'error',
            'severity' => $this->getSeverityName($severity),
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'time' => date('Y-m-d H:i:s'),
            'trace' => $this->isDebugMode ? debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS) : null
        ];
        
        $this->logError($error);
        
        // 在调试模式下显示错误
        if ($this->isDebugMode) {
            $this->displayError($error);
        }
        
        return true;
    }
    
    /**
     * 处理未捕获的异常
     */
    public function handleException(Throwable $exception): void {
        $error = [
            'type' => 'exception',
            'class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'time' => date('Y-m-d H:i:s'),
            'trace' => $this->isDebugMode ? $exception->getTrace() : null
        ];
        
        $this->logError($error);
        
        if ($this->isDebugMode) {
            $this->displayError($error);
        } else {
            // 生产环境显示友好错误页面
            $this->displayFriendlyError();
        }
    }
    
    /**
     * 处理致命错误
     */
    public function handleShutdown(): void {
        $error = error_get_last();
        
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorInfo = [
                'type' => 'fatal',
                'severity' => $this->getSeverityName($error['type']),
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'time' => date('Y-m-d H:i:s')
            ];
            
            $this->logError($errorInfo);
            
            if ($this->isDebugMode) {
                $this->displayError($errorInfo);
            } else {
                $this->displayFriendlyError();
            }
        }
    }
    
    /**
     * 记录错误到日志
     */
    private function logError(array $error): void {
        $this->errorLog[] = $error;
        
        $logEntry = sprintf(
            "[%s] %s: %s in %s:%d\n",
            $error['time'],
            $error['type'],
            $error['message'],
            $error['file'],
            $error['line']
        );
        
        $logFile = $this->logPath . 'error_' . date('Y-m-d') . '.log';
        @file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 显示错误信息（调试模式）
     */
    private function displayError(array $error): void {
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
            // AJAX请求返回JSON
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line']
            ]);
        } else {
            // 普通请求显示HTML
            echo "<div style='background:#f8d7da;color:#721c24;padding:10px;margin:10px;border:1px solid #f5c6cb;border-radius:4px;'>";
            echo "<strong>{$error['type']}</strong>: {$error['message']}<br>";
            echo "File: {$error['file']} Line: {$error['line']}<br>";
            echo "Time: {$error['time']}";
            echo "</div>";
        }
    }
    
    /**
     * 显示友好错误页面（生产模式）
     */
    private function displayFriendlyError(): void {
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => '系统暂时不可用，请稍后再试'
            ]);
        } else {
            echo "<h1>系统暂时不可用</h1><p>我们正在修复问题，请稍后再试。</p>";
        }
    }
    
    /**
     * 获取错误级别名称
     */
    private function getSeverityName(int $severity): string {
        $levels = [
            E_ERROR => 'Fatal Error',
            E_WARNING => 'Warning',
            E_PARSE => 'Parse Error',
            E_NOTICE => 'Notice',
            E_CORE_ERROR => 'Core Error',
            E_CORE_WARNING => 'Core Warning',
            E_COMPILE_ERROR => 'Compile Error',
            E_COMPILE_WARNING => 'Compile Warning',
            E_USER_ERROR => 'User Error',
            E_USER_WARNING => 'User Warning',
            E_USER_NOTICE => 'User Notice',
            E_STRICT => 'Strict Standards',
            E_RECOVERABLE_ERROR => 'Recoverable Error',
            E_DEPRECATED => 'Deprecated',
            E_USER_DEPRECATED => 'User Deprecated'
        ];
        
        return $levels[$severity] ?? 'Unknown Error';
    }
    
    /**
     * 获取错误统计
     */
    public function getErrorStats(): array {
        $stats = [];
        foreach ($this->errorLog as $error) {
            $type = $error['type'];
            $stats[$type] = ($stats[$type] ?? 0) + 1;
        }
        return $stats;
    }
    
    /**
     * 清理旧日志文件
     */
    public function cleanOldLogs(int $days = 30): void {
        $cutoff = time() - ($days * 24 * 3600);
        $pattern = $this->logPath . 'error_*.log';
        
        foreach (glob($pattern) as $file) {
            if (filemtime($file) < $cutoff) {
                @unlink($file);
            }
        }
    }
}

// 自动注册错误处理器
if (class_exists('ModernErrorHandler')) {
    ModernErrorHandler::getInstance()->register();
}

?>
