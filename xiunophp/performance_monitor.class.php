<?php

/**
 * 性能监控类
 * 监控页面加载时间、内存使用、数据库查询等性能指标
 */
class PerformanceMonitor {
    
    private static $instance = null;
    private $startTime;
    private $startMemory;
    private $checkpoints = [];
    private $queries = [];
    private $isEnabled = false;
    
    private function __construct() {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
        $this->isEnabled = defined('DEBUG') && DEBUG;
    }
    
    /**
     * 获取单例实例
     */
    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 添加检查点
     */
    public function checkpoint(string $name): void {
        if (!$this->isEnabled) return;
        
        $this->checkpoints[] = [
            'name' => $name,
            'time' => microtime(true),
            'memory' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
    }
    
    /**
     * 记录数据库查询
     */
    public function logQuery(string $sql, float $time): void {
        if (!$this->isEnabled) return;
        
        $this->queries[] = [
            'sql' => $sql,
            'time' => $time,
            'timestamp' => microtime(true)
        ];
    }
    
    /**
     * 获取性能报告
     */
    public function getReport(): array {
        if (!$this->isEnabled) {
            return ['enabled' => false];
        }
        
        $currentTime = microtime(true);
        $currentMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        $report = [
            'enabled' => true,
            'total_time' => round(($currentTime - $this->startTime) * 1000, 2), // ms
            'memory_usage' => [
                'start' => $this->formatBytes($this->startMemory),
                'current' => $this->formatBytes($currentMemory),
                'peak' => $this->formatBytes($peakMemory),
                'used' => $this->formatBytes($currentMemory - $this->startMemory)
            ],
            'checkpoints' => $this->getCheckpointReport(),
            'queries' => $this->getQueryReport(),
            'system_info' => $this->getSystemInfo()
        ];
        
        return $report;
    }
    
    /**
     * 获取检查点报告
     */
    private function getCheckpointReport(): array {
        $report = [];
        $lastTime = $this->startTime;
        $lastMemory = $this->startMemory;
        
        foreach ($this->checkpoints as $checkpoint) {
            $report[] = [
                'name' => $checkpoint['name'],
                'elapsed_time' => round(($checkpoint['time'] - $this->startTime) * 1000, 2),
                'step_time' => round(($checkpoint['time'] - $lastTime) * 1000, 2),
                'memory' => $this->formatBytes($checkpoint['memory']),
                'memory_diff' => $this->formatBytes($checkpoint['memory'] - $lastMemory),
                'peak_memory' => $this->formatBytes($checkpoint['peak_memory'])
            ];
            
            $lastTime = $checkpoint['time'];
            $lastMemory = $checkpoint['memory'];
        }
        
        return $report;
    }
    
    /**
     * 获取查询报告
     */
    private function getQueryReport(): array {
        $totalTime = 0;
        $slowQueries = [];
        
        foreach ($this->queries as $query) {
            $totalTime += $query['time'];
            
            // 记录慢查询（超过100ms）
            if ($query['time'] > 0.1) {
                $slowQueries[] = [
                    'sql' => substr($query['sql'], 0, 200) . (strlen($query['sql']) > 200 ? '...' : ''),
                    'time' => round($query['time'] * 1000, 2)
                ];
            }
        }
        
        return [
            'total_queries' => count($this->queries),
            'total_time' => round($totalTime * 1000, 2),
            'average_time' => count($this->queries) > 0 ? round(($totalTime / count($this->queries)) * 1000, 2) : 0,
            'slow_queries' => $slowQueries
        ];
    }
    
    /**
     * 获取系统信息
     */
    private function getSystemInfo(): array {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'loaded_extensions' => count(get_loaded_extensions()),
            'max_execution_time' => ini_get('max_execution_time'),
            'memory_limit' => ini_get('memory_limit'),
            'post_max_size' => ini_get('post_max_size'),
            'upload_max_filesize' => ini_get('upload_max_filesize')
        ];
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes(int $bytes): string {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 输出性能报告到HTML
     */
    public function displayReport(): void {
        if (!$this->isEnabled) return;
        
        $report = $this->getReport();
        
        echo '<div style="background:#f8f9fa;border:1px solid #dee2e6;padding:15px;margin:10px;border-radius:5px;font-family:monospace;font-size:12px;">';
        echo '<h4 style="margin:0 0 10px 0;color:#495057;">性能监控报告</h4>';
        
        echo '<div style="margin-bottom:10px;">';
        echo '<strong>总执行时间:</strong> ' . $report['total_time'] . ' ms<br>';
        echo '<strong>内存使用:</strong> ' . $report['memory_usage']['used'] . ' (峰值: ' . $report['memory_usage']['peak'] . ')<br>';
        echo '</div>';
        
        if (!empty($report['queries']['total_queries'])) {
            echo '<div style="margin-bottom:10px;">';
            echo '<strong>数据库查询:</strong> ' . $report['queries']['total_queries'] . ' 次<br>';
            echo '<strong>查询总时间:</strong> ' . $report['queries']['total_time'] . ' ms<br>';
            echo '<strong>平均查询时间:</strong> ' . $report['queries']['average_time'] . ' ms<br>';
            echo '</div>';
        }
        
        if (!empty($report['queries']['slow_queries'])) {
            echo '<div style="margin-bottom:10px;">';
            echo '<strong>慢查询 (>100ms):</strong><br>';
            foreach ($report['queries']['slow_queries'] as $query) {
                echo '<div style="margin-left:10px;color:#dc3545;">';
                echo $query['time'] . 'ms: ' . htmlspecialchars($query['sql']) . '<br>';
                echo '</div>';
            }
            echo '</div>';
        }
        
        if (!empty($report['checkpoints'])) {
            echo '<div>';
            echo '<strong>检查点:</strong><br>';
            foreach ($report['checkpoints'] as $checkpoint) {
                echo '<div style="margin-left:10px;">';
                echo $checkpoint['name'] . ': ' . $checkpoint['elapsed_time'] . 'ms (+' . $checkpoint['step_time'] . 'ms)<br>';
                echo '</div>';
            }
            echo '</div>';
        }
        
        echo '</div>';
    }
    
    /**
     * 输出性能报告到控制台（JSON格式）
     */
    public function logReport(): void {
        if (!$this->isEnabled) return;
        
        $report = $this->getReport();
        error_log('Performance Report: ' . json_encode($report, JSON_PRETTY_PRINT));
    }
}

// 自动初始化性能监控
if (class_exists('PerformanceMonitor')) {
    PerformanceMonitor::getInstance()->checkpoint('System Start');
}

?>
