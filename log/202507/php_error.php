<?php exit;?>	2025-07-05 11:02:14	127.0.0.1	/test_main_page.php	0	Error[8192]: session_set_save_handler(): Providing individual callbacks instead of an object implementing SessionHandlerInterface is deprecated, File: /Users/<USER>/Desktop/aaa/model/session.func.php, Line: 206
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 189
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 191
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 192
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 193
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 194
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 195
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 196
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 197
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 199
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 200
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 201
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[8192]: session_set_save_handler(): Providing individual callbacks instead of an object implementing SessionHandlerInterface is deprecated, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: session_set_save_handler(): Session save handler cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: session_start(): Session cannot be started after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 210
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "host", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "user", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "password", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "name", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "charset", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "engine", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "host", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "user", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "password", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "name", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "charset", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:15:57	0.0.0.0		0	Error[2]: Undefined array key "engine", File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 189
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 191
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 192
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 193
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 194
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 195
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 196
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 197
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 199
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 200
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 201
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[8192]: session_set_save_handler(): Providing individual callbacks instead of an object implementing SessionHandlerInterface is deprecated, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: session_set_save_handler(): Session save handler cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:16:29	0.0.0.0		0	Error[2]: session_start(): Session cannot be started after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 210
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 189
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 191
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 192
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 193
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 194
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 195
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 196
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 197
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 199
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 200
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 201
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[8192]: session_set_save_handler(): Providing individual callbacks instead of an object implementing SessionHandlerInterface is deprecated, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: session_set_save_handler(): Session save handler cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:16:52	0.0.0.0		0	Error[2]: session_start(): Session cannot be started after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 210
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 189
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 191
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 192
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 193
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 194
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 195
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 196
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 197
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 199
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 200
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 201
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[8192]: session_set_save_handler(): Providing individual callbacks instead of an object implementing SessionHandlerInterface is deprecated, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: session_set_save_handler(): Session save handler cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:17:17	0.0.0.0		0	Error[2]: session_start(): Session cannot be started after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 210
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 189
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 191
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 192
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 193
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 194
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 195
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 196
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 197
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 199
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 200
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 201
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[8192]: session_set_save_handler(): Providing individual callbacks instead of an object implementing SessionHandlerInterface is deprecated, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: session_set_save_handler(): Session save handler cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: session_start(): Session cannot be started after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 210
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: Undefined property: db_pdo_sqlite::$sqls, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:19:41	0.0.0.0		0	Error[2]: Undefined property: db_pdo_sqlite::$sqls, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 42
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 189
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 191
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 192
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 193
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 194
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 195
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 196
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 197
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 199
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 200
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 201
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[8192]: session_set_save_handler(): Providing individual callbacks instead of an object implementing SessionHandlerInterface is deprecated, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: session_set_save_handler(): Session save handler cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: session_start(): Session cannot be started after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 210
<?php exit;?>	2025-07-05 11:20:31	0.0.0.0		0	Error[2]: Undefined array key 0, File: /Users/<USER>/Desktop/aaa/tmp/index.inc.php, Line: 22
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 189
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 191
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 192
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 193
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 194
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 195
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 196
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 197
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 199
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 200
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: ini_set(): Session ini settings cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 201
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: session_set_save_handler(): Providing individual callbacks instead of an object implementing SessionHandlerInterface is deprecated, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: session_set_save_handler(): Session save handler cannot be changed after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 203
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: session_start(): Session cannot be started after headers have already been sent, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 210
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[2]: Undefined array key 0, File: /Users/<USER>/Desktop/aaa/tmp/index.inc.php, Line: 22
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: stripos(): Passing null to parameter #1 ($haystack) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: stripos(): Passing null to parameter #1 ($haystack) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: stripos(): Passing null to parameter #1 ($haystack) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:21:07	0.0.0.0		0	Error[8192]: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:37:26	0.0.0.0		0	Error[2]: Cannot modify header information - headers already sent by (output started at /Users/<USER>/Desktop/aaa/debug_admin.php:5), File: /Users/<USER>/Desktop/aaa/boyouphp/boyouphp.min.php, Line: 57
<?php exit;?>	2025-07-05 11:38:43	0.0.0.0		0	Error[8192]: md5(): Passing null to parameter #1 ($string) of type string is deprecated, File: /Users/<USER>/Desktop/aaa/admin/admin.func.php, Line: 10
<?php exit;?>	2025-07-05 11:48:43	127.0.0.1	/	0	Error[2]: Undefined array key 0, File: /Users/<USER>/Desktop/aaa/tmp/index.inc.php, Line: 25
<?php exit;?>	2025-07-05 11:48:44	127.0.0.1	/favicon.ico	0	Error[2]: Undefined array key 0, File: /Users/<USER>/Desktop/aaa/tmp/index.inc.php, Line: 25
<?php exit;?>	2025-07-05 11:54:14	127.0.0.1	/?user-login.htm	0	Error[2]: Undefined array key 0, File: /Users/<USER>/Desktop/aaa/tmp/index.inc.php, Line: 25
<?php exit;?>	2025-07-05 11:54:15	127.0.0.1	/favicon.ico	0	Error[2]: Undefined array key 0, File: /Users/<USER>/Desktop/aaa/tmp/index.inc.php, Line: 25
<?php exit;?>	2025-07-05 11:54:17	127.0.0.1	/	0	Error[2]: Undefined array key 0, File: /Users/<USER>/Desktop/aaa/tmp/index.inc.php, Line: 25
<?php exit;?>	2025-07-05 11:54:17	127.0.0.1	/favicon.ico	0	Error[2]: Undefined array key 0, File: /Users/<USER>/Desktop/aaa/tmp/index.inc.php, Line: 25
<?php exit;?>	2025-07-05 11:54:27	127.0.0.1	/.well-known/appspecific/com.chrome.devtools.json	0	Error[2]: Undefined array key 0, File: /Users/<USER>/Desktop/aaa/tmp/index.inc.php, Line: 25
<?php exit;?>	2025-07-05 12:08:00	127.0.0.1	/.well-known/appspecific/com.chrome.devtools.json	0	Error[2]: session_start(): Failed to decode session object. Session has been destroyed, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 266
<?php exit;?>	2025-07-05 12:10:18	127.0.0.1	/?user-login.htm	0	Error[2]: session_start(): Failed to decode session object. Session has been destroyed, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 266
<?php exit;?>	2025-07-05 12:10:18	127.0.0.1	/.well-known/appspecific/com.chrome.devtools.json	0	Error[2]: session_start(): Failed to decode session object. Session has been destroyed, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 266
<?php exit;?>	2025-07-05 12:11:05	127.0.0.1	/.well-known/appspecific/com.chrome.devtools.json	0	Error[2]: session_start(): Failed to decode session object. Session has been destroyed, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 266
<?php exit;?>	2025-07-05 12:11:09	127.0.0.1	/.well-known/appspecific/com.chrome.devtools.json	0	Error[2]: session_start(): Failed to decode session object. Session has been destroyed, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 266
<?php exit;?>	2025-07-05 12:11:15	127.0.0.1	/.well-known/appspecific/com.chrome.devtools.json	0	Error[2]: session_start(): Failed to decode session object. Session has been destroyed, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 266
<?php exit;?>	2025-07-05 12:11:18	127.0.0.1	/.well-known/appspecific/com.chrome.devtools.json	0	Error[2]: session_start(): Failed to decode session object. Session has been destroyed, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 266
<?php exit;?>	2025-07-05 12:11:31	127.0.0.1	/.well-known/appspecific/com.chrome.devtools.json	0	Error[2]: session_start(): Failed to decode session object. Session has been destroyed, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 266
<?php exit;?>	2025-07-05 12:13:05	127.0.0.1	/.well-known/appspecific/com.chrome.devtools.json	0	Error[2]: session_start(): Failed to decode session object. Session has been destroyed, File: /Users/<USER>/Desktop/aaa/tmp/model_session.func.php, Line: 266
