<?php
/**
 * 环境切换脚本
 * 用于在开发环境和生产环境之间切换
 */

if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line.');
}

$environment = isset($argv[1]) ? $argv[1] : '';

if (!in_array($environment, ['development', 'production'])) {
    echo "Usage: php switch_environment.php [development|production]\n";
    echo "\n";
    echo "Available environments:\n";
    echo "  development - 开发环境 (DEBUG=1, 显示错误)\n";
    echo "  production  - 生产环境 (DEBUG=0, 隐藏错误)\n";
    exit(1);
}

$app_path = __DIR__ . '/';
$conf_file = $app_path . 'conf/conf.php';
$backup_file = $app_path . 'conf/conf.backup.php';

echo "切换到 {$environment} 环境...\n";

// 备份当前配置
if (file_exists($conf_file)) {
    copy($conf_file, $backup_file);
    echo "已备份当前配置到 conf/conf.backup.php\n";
}

if ($environment === 'development') {
    // 开发环境配置
    $config = [
        'debug_mode' => true,
        'error_display' => true,
        'error_logging' => true,
        'static_version' => '?dev.' . time(),
        'cache_expire_time' => 60, // 短缓存时间便于调试
    ];
    
    // 修改 index.php 中的 DEBUG 常量
    $index_content = file_get_contents($app_path . 'index.php');
    $index_content = preg_replace('/!defined\(\'DEBUG\'\) AND define\(\'DEBUG\', \d+\);/', 
                                  '!defined(\'DEBUG\') AND define(\'DEBUG\', 1);', 
                                  $index_content);
    file_put_contents($app_path . 'index.php', $index_content);
    
    echo "✓ 已切换到开发环境\n";
    echo "  - DEBUG = 1\n";
    echo "  - 错误显示: 开启\n";
    echo "  - 缓存时间: 60秒\n";
    
} else {
    // 生产环境配置
    if (file_exists($app_path . 'conf/production.php')) {
        copy($app_path . 'conf/production.php', $conf_file);
        echo "已应用生产环境配置\n";
    }
    
    // 修改 index.php 中的 DEBUG 常量
    $index_content = file_get_contents($app_path . 'index.php');
    $index_content = preg_replace('/!defined\(\'DEBUG\'\) AND define\(\'DEBUG\', \d+\);/', 
                                  '!defined(\'DEBUG\') AND define(\'DEBUG\', 0);', 
                                  $index_content);
    file_put_contents($app_path . 'index.php', $index_content);
    
    // 清理临时文件
    $tmp_path = $app_path . 'tmp/';
    if (is_dir($tmp_path)) {
        $files = glob($tmp_path . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        echo "已清理临时文件\n";
    }
    
    echo "✓ 已切换到生产环境\n";
    echo "  - DEBUG = 0\n";
    echo "  - 错误显示: 关闭\n";
    echo "  - 缓存时间: 3600秒\n";
    echo "  - 已清理临时文件\n";
}

echo "\n环境切换完成！\n";
echo "请重启 Web 服务器以使配置生效。\n";
?>
