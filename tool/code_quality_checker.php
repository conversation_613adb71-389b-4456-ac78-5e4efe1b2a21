<?php

/**
 * 代码质量检查工具
 * 检查PHP代码中的潜在问题和过时语法
 */

class CodeQualityChecker {
    
    private $issues = [];
    private $checkedFiles = 0;
    
    /**
     * 检查目录中的所有PHP文件
     */
    public function checkDirectory(string $directory): array {
        $this->issues = [];
        $this->checkedFiles = 0;
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $this->checkFile($file->getPathname());
            }
        }
        
        return $this->getReport();
    }
    
    /**
     * 检查单个文件
     */
    public function checkFile(string $filePath): void {
        if (!file_exists($filePath)) {
            return;
        }
        
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);
        
        $this->checkedFiles++;
        
        foreach ($lines as $lineNumber => $line) {
            $this->checkLine($filePath, $lineNumber + 1, $line);
        }
        
        // 检查整个文件的问题
        $this->checkFileContent($filePath, $content);
    }
    
    /**
     * 检查单行代码
     */
    private function checkLine(string $file, int $lineNumber, string $line): void {
        $trimmedLine = trim($line);
        
        // 检查已弃用的函数
        $deprecatedFunctions = [
            'mysql_connect', 'mysql_query', 'mysql_fetch_array', 'mysql_fetch_assoc',
            'mysql_num_rows', 'mysql_insert_id', 'mysql_affected_rows', 'mysql_close',
            'ereg', 'eregi', 'split', 'each', 'create_function'
        ];
        
        foreach ($deprecatedFunctions as $func) {
            if (preg_match('/\b' . preg_quote($func) . '\s*\(/', $line)) {
                $this->addIssue($file, $lineNumber, 'deprecated_function', 
                    "使用了已弃用的函数: $func", 'high');
            }
        }
        
        // 检查旧数组语法
        if (preg_match('/\barray\s*\(/', $line)) {
            $this->addIssue($file, $lineNumber, 'old_array_syntax', 
                '使用了旧式数组语法 array()，建议使用 []', 'low');
        }
        
        // 检查可能的SQL注入
        if (preg_match('/\$_(?:GET|POST|REQUEST)\[.*?\].*?(?:SELECT|INSERT|UPDATE|DELETE)/i', $line)) {
            $this->addIssue($file, $lineNumber, 'sql_injection_risk', 
                '可能存在SQL注入风险：直接使用用户输入构建SQL', 'high');
        }
        
        // 检查可能的XSS
        if (preg_match('/echo\s+\$_(?:GET|POST|REQUEST)\[/', $line)) {
            $this->addIssue($file, $lineNumber, 'xss_risk', 
                '可能存在XSS风险：直接输出用户输入', 'medium');
        }
        
        // 检查eval使用
        if (preg_match('/\beval\s*\(/', $line)) {
            $this->addIssue($file, $lineNumber, 'eval_usage', 
                '使用了eval()函数，存在安全风险', 'high');
        }
        
        // 检查全局变量过度使用
        if (preg_match('/global\s+\$\w+/', $line)) {
            $this->addIssue($file, $lineNumber, 'global_usage', 
                '使用了global变量，建议使用依赖注入', 'low');
        }
        
        // 检查错误抑制符过度使用
        if (preg_match('/@\w+\s*\(/', $line) && !preg_match('/@(mkdir|file_get_contents|file_put_contents|unlink)/', $line)) {
            $this->addIssue($file, $lineNumber, 'error_suppression', 
                '使用了错误抑制符@，可能隐藏重要错误', 'medium');
        }
    }
    
    /**
     * 检查整个文件内容
     */
    private function checkFileContent(string $file, string $content): void {
        // 检查文件编码
        if (!mb_check_encoding($content, 'UTF-8')) {
            $this->addIssue($file, 1, 'encoding_issue', 
                '文件编码不是UTF-8', 'medium');
        }
        
        // 检查PHP标签
        if (strpos($content, '<?') !== false && strpos($content, '<?php') === false) {
            $this->addIssue($file, 1, 'short_php_tag', 
                '使用了短PHP标签，建议使用完整标签 <?php', 'low');
        }
        
        // 检查文件大小
        if (strlen($content) > 100000) { // 100KB
            $this->addIssue($file, 1, 'large_file', 
                '文件过大，建议拆分为更小的文件', 'low');
        }
        
        // 检查函数复杂度（简单检查）
        preg_match_all('/function\s+\w+\s*\([^)]*\)\s*\{/', $content, $matches, PREG_OFFSET_CAPTURE);
        foreach ($matches[0] as $match) {
            $functionStart = $match[1];
            $braceCount = 0;
            $functionEnd = $functionStart;
            
            for ($i = $functionStart; $i < strlen($content); $i++) {
                if ($content[$i] === '{') $braceCount++;
                if ($content[$i] === '}') $braceCount--;
                if ($braceCount === 0) {
                    $functionEnd = $i;
                    break;
                }
            }
            
            $functionContent = substr($content, $functionStart, $functionEnd - $functionStart);
            $lineCount = substr_count($functionContent, "\n");
            
            if ($lineCount > 50) {
                $lineNumber = substr_count(substr($content, 0, $functionStart), "\n") + 1;
                $this->addIssue($file, $lineNumber, 'complex_function', 
                    "函数过长 ($lineCount 行)，建议拆分", 'medium');
            }
        }
    }
    
    /**
     * 添加问题
     */
    private function addIssue(string $file, int $line, string $type, string $message, string $severity): void {
        $this->issues[] = [
            'file' => $file,
            'line' => $line,
            'type' => $type,
            'message' => $message,
            'severity' => $severity
        ];
    }
    
    /**
     * 获取检查报告
     */
    public function getReport(): array {
        $severityCount = ['high' => 0, 'medium' => 0, 'low' => 0];
        $typeCount = [];
        
        foreach ($this->issues as $issue) {
            $severityCount[$issue['severity']]++;
            $typeCount[$issue['type']] = ($typeCount[$issue['type']] ?? 0) + 1;
        }
        
        return [
            'summary' => [
                'checked_files' => $this->checkedFiles,
                'total_issues' => count($this->issues),
                'severity_breakdown' => $severityCount,
                'type_breakdown' => $typeCount
            ],
            'issues' => $this->issues
        ];
    }
    
    /**
     * 输出HTML报告
     */
    public function displayHtmlReport(): void {
        $report = $this->getReport();
        
        echo '<div style="font-family: Arial, sans-serif; margin: 20px;">';
        echo '<h2>代码质量检查报告</h2>';
        
        echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">';
        echo '<h3>概要</h3>';
        echo '<p>检查文件数: ' . $report['summary']['checked_files'] . '</p>';
        echo '<p>发现问题数: ' . $report['summary']['total_issues'] . '</p>';
        echo '<p>严重程度分布:</p>';
        echo '<ul>';
        echo '<li>高危: ' . $report['summary']['severity_breakdown']['high'] . '</li>';
        echo '<li>中等: ' . $report['summary']['severity_breakdown']['medium'] . '</li>';
        echo '<li>低危: ' . $report['summary']['severity_breakdown']['low'] . '</li>';
        echo '</ul>';
        echo '</div>';
        
        if (!empty($report['issues'])) {
            echo '<h3>详细问题列表</h3>';
            echo '<table style="width: 100%; border-collapse: collapse;">';
            echo '<tr style="background: #e9ecef;">';
            echo '<th style="border: 1px solid #dee2e6; padding: 8px;">文件</th>';
            echo '<th style="border: 1px solid #dee2e6; padding: 8px;">行号</th>';
            echo '<th style="border: 1px solid #dee2e6; padding: 8px;">类型</th>';
            echo '<th style="border: 1px solid #dee2e6; padding: 8px;">严重程度</th>';
            echo '<th style="border: 1px solid #dee2e6; padding: 8px;">描述</th>';
            echo '</tr>';
            
            foreach ($report['issues'] as $issue) {
                $severityColor = [
                    'high' => '#dc3545',
                    'medium' => '#fd7e14', 
                    'low' => '#28a745'
                ];
                
                echo '<tr>';
                echo '<td style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($issue['file']) . '</td>';
                echo '<td style="border: 1px solid #dee2e6; padding: 8px;">' . $issue['line'] . '</td>';
                echo '<td style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($issue['type']) . '</td>';
                echo '<td style="border: 1px solid #dee2e6; padding: 8px; color: ' . $severityColor[$issue['severity']] . ';">' . $issue['severity'] . '</td>';
                echo '<td style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($issue['message']) . '</td>';
                echo '</tr>';
            }
            
            echo '</table>';
        }
        
        echo '</div>';
    }
}

// 如果直接运行此脚本
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $checker = new CodeQualityChecker();
    $directory = $argv[1] ?? '../';
    
    echo "正在检查目录: $directory\n";
    $report = $checker->checkDirectory($directory);
    
    echo "\n=== 代码质量检查报告 ===\n";
    echo "检查文件数: " . $report['summary']['checked_files'] . "\n";
    echo "发现问题数: " . $report['summary']['total_issues'] . "\n";
    echo "严重程度分布:\n";
    echo "  高危: " . $report['summary']['severity_breakdown']['high'] . "\n";
    echo "  中等: " . $report['summary']['severity_breakdown']['medium'] . "\n";
    echo "  低危: " . $report['summary']['severity_breakdown']['low'] . "\n";
    
    if (!empty($report['issues'])) {
        echo "\n详细问题:\n";
        foreach ($report['issues'] as $issue) {
            echo sprintf("[%s] %s:%d - %s\n", 
                strtoupper($issue['severity']), 
                $issue['file'], 
                $issue['line'], 
                $issue['message']
            );
        }
    }
}

?>
