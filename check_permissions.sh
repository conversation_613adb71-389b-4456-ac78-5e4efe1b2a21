#!/bin/bash

echo "=== Boyou BBS 6.1 权限检查 ==="
echo

echo "敏感文件权限检查:"
files=("conf/conf.php" "data/boyou_bbs.db" ".htaccess")
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        perms=$(stat -c "%a" "$file" 2>/dev/null || stat -f "%A" "$file" 2>/dev/null)
        echo "  $file: $perms"
    fi
done

echo
echo "可写目录权限检查:"
dirs=("tmp" "upload" "log" "data")
for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        perms=$(stat -c "%a" "$dir" 2>/dev/null || stat -f "%A" "$dir" 2>/dev/null)
        echo "  $dir/: $perms"
    fi
done

echo
echo "检查完成"
