<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Testing Index.php Flow ===\n";

try {
    // 模拟 index.php 的执行流程
    !defined('DEBUG') AND define('DEBUG', 1);
    define('APP_PATH', __DIR__.'/');
    !defined('ADMIN_PATH') AND define('ADMIN_PATH', APP_PATH.'admin/');
    !defined('BOYOUPHP_PATH') AND define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');

    echo "1. Constants defined\n";

    // 检查是否已安装
    if (!file_exists(APP_PATH.'data/install.lock')) {
        echo "ERROR: Install lock not found\n";
        exit;
    }
    echo "2. Install lock found\n";

    $conf = (@include APP_PATH.'conf/conf.php') OR exit('ERROR: Config file not found');
    echo "3. Config loaded\n";

    // 兼容性设置
    !isset($conf['user_create_on']) AND $conf['user_create_on'] = 1;
    !isset($conf['logo_mobile_url']) AND $conf['logo_mobile_url'] = 'view/img/logo.png';
    !isset($conf['logo_pc_url']) AND $conf['logo_pc_url'] = 'view/img/logo.png';
    !isset($conf['logo_water_url']) AND $conf['logo_water_url'] = 'view/img/water-small.png';
    $conf['version'] = '4.0.4';

    echo "4. Config compatibility set\n";

    // 转换为绝对路径
    substr($conf['log_path'], 0, 2) == './' AND $conf['log_path'] = APP_PATH.$conf['log_path']; 
    substr($conf['tmp_path'], 0, 2) == './' AND $conf['tmp_path'] = APP_PATH.$conf['tmp_path']; 
    substr($conf['upload_path'], 0, 2) == './' AND $conf['upload_path'] = APP_PATH.$conf['upload_path']; 

    $_SERVER['conf'] = $conf;
    echo "5. Paths converted and conf set\n";

    // 包含 BoyouPHP
    if(DEBUG > 1) {
        include BOYOUPHP_PATH.'boyouphp.php';
    } else {
        include BOYOUPHP_PATH.'boyouphp.min.php';
    }
    echo "6. BoyouPHP included\n";

    // 包含插件函数
    include APP_PATH.'model/plugin.func.php';
    echo "7. Plugin functions included\n";

    // 检查 _include 函数是否存在
    if (!function_exists('_include')) {
        echo "ERROR: _include function not found\n";
        exit;
    }

    // 包含模型
    include _include(APP_PATH.'model.inc.php');
    echo "8. Model included\n";

    // 包含主页面
    include _include(APP_PATH.'index.inc.php');
    echo "9. Index inc included\n";

    echo "=== SUCCESS: All files loaded ===\n";

} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
