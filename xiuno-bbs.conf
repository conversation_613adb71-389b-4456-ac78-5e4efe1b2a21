server {
    listen 80;
    server_name localhost xiuno.local;
    root /Users/<USER>/Desktop/aaa;
    index index.php index.html index.htm;

    # 安全配置
    server_tokens off;
    
    # 日志配置
    access_log /usr/local/var/log/nginx/xiuno-bbs.access.log;
    error_log /usr/local/var/log/nginx/xiuno-bbs.error.log;

    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问配置文件和备份文件
    location ~* \.(conf|sql|log|txt|bak|backup|old)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问安装目录（生产环境）
    location ^~ /install/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问工具目录
    location ^~ /tool/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # URL 重写规则 - Xiuno BBS 友好URL
    location / {
        try_files $uri $uri/ @rewrite;
    }

    location @rewrite {
        rewrite ^/(.*)$ /index.php?$1 last;
    }

    # PHP 处理
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 安全配置
        fastcgi_param PHP_VALUE "open_basedir=$document_root:/tmp/:/var/tmp/";
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }

    # 上传目录安全配置
    location ^~ /upload/ {
        location ~ \.php$ {
            deny all;
        }
    }

    # 临时目录安全配置
    location ^~ /tmp/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 日志目录安全配置
    location ^~ /log/ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
