<?php

echo "=== 修复数据库结构 ===\n\n";

try {
    $pdo = new PDO('sqlite:data/boyou_bbs.db');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "1. 检查当前数据库结构...\n";
    
    // 检查用户表结构
    $stmt = $pdo->query("PRAGMA table_info(bbs_user)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "当前用户表字段:\n";
    foreach ($columns as $column) {
        echo "  - {$column['name']} ({$column['type']})\n";
    }
    
    // 检查是否需要添加缺失的字段
    $existingColumns = array_column($columns, 'name');
    
    $requiredColumns = [
        'salt' => 'VARCHAR(32) DEFAULT ""',
        'gid' => 'INTEGER DEFAULT 1',
        'create_ip' => 'INTEGER DEFAULT 0',
        'login_ip' => 'INTEGER DEFAULT 0',
        'logins' => 'INTEGER DEFAULT 0',
        'threads' => 'INTEGER DEFAULT 0',
        'posts' => 'INTEGER DEFAULT 0',
        'credits' => 'INTEGER DEFAULT 0',
        'avatar_url' => 'VARCHAR(255) DEFAULT ""',
        'mobile' => 'VARCHAR(20) DEFAULT ""'
    ];
    
    echo "\n2. 添加缺失的字段...\n";
    
    foreach ($requiredColumns as $columnName => $columnDef) {
        if (!in_array($columnName, $existingColumns)) {
            try {
                $sql = "ALTER TABLE bbs_user ADD COLUMN $columnName $columnDef";
                $pdo->exec($sql);
                echo "  ✓ 添加字段: $columnName\n";
            } catch (PDOException $e) {
                echo "  ✗ 添加字段失败 $columnName: " . $e->getMessage() . "\n";
            }
        } else {
            echo "  ✓ 字段已存在: $columnName\n";
        }
    }
    
    // 处理 group_id 到 gid 的映射
    if (in_array('group_id', $existingColumns) && !in_array('gid', $existingColumns)) {
        echo "\n3. 处理 group_id 到 gid 的映射...\n";
        try {
            $pdo->exec("ALTER TABLE bbs_user ADD COLUMN gid INTEGER DEFAULT 1");
            $pdo->exec("UPDATE bbs_user SET gid = group_id");
            echo "  ✓ 已将 group_id 数据复制到 gid 字段\n";
        } catch (PDOException $e) {
            echo "  ✗ 映射失败: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n4. 检查其他核心表...\n";
    
    // 检查其他必要的表
    $requiredTables = [
        'bbs_forum' => '版块表',
        'bbs_thread' => '主题表', 
        'bbs_post' => '帖子表',
        'bbs_session' => '会话表',
        'bbs_config' => '配置表'
    ];
    
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($requiredTables as $tableName => $description) {
        if (in_array($tableName, $existingTables)) {
            echo "  ✓ $description 存在\n";
        } else {
            echo "  ⚠️  $description 不存在，需要创建\n";
            createMissingTable($pdo, $tableName);
        }
    }
    
    echo "\n5. 创建缺失的配置表...\n";
    
    if (!in_array('bbs_config', $existingTables)) {
        $sql = "CREATE TABLE bbs_config (
            k VARCHAR(32) PRIMARY KEY,
            v TEXT NOT NULL,
            expiry INTEGER DEFAULT 0
        )";
        
        try {
            $pdo->exec($sql);
            echo "  ✓ 创建配置表成功\n";
            
            // 插入默认配置
            $defaultConfigs = [
                ['user_create_on', '1'],
                ['user_create_email_on', '0'],
                ['user_resetpw_on', '1'],
                ['sitename', 'Boyou BBS'],
                ['siteurl', 'http://localhost:8000/'],
                ['version', '6.1']
            ];
            
            $stmt = $pdo->prepare("INSERT OR IGNORE INTO bbs_config (k, v) VALUES (?, ?)");
            foreach ($defaultConfigs as $config) {
                $stmt->execute($config);
            }
            echo "  ✓ 插入默认配置成功\n";
            
        } catch (PDOException $e) {
            echo "  ✗ 创建配置表失败: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n6. 创建必要的索引...\n";
    
    $indexes = [
        'idx_user_email' => 'CREATE INDEX IF NOT EXISTS idx_user_email ON bbs_user(email)',
        'idx_user_username' => 'CREATE INDEX IF NOT EXISTS idx_user_username ON bbs_user(username)',
        'idx_user_login_date' => 'CREATE INDEX IF NOT EXISTS idx_user_login_date ON bbs_user(login_date)'
    ];
    
    foreach ($indexes as $indexName => $sql) {
        try {
            $pdo->exec($sql);
            echo "  ✓ 创建索引: $indexName\n";
        } catch (PDOException $e) {
            echo "  ✗ 创建索引失败 $indexName: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n7. 验证数据库结构...\n";
    
    // 重新检查用户表结构
    $stmt = $pdo->query("PRAGMA table_info(bbs_user)");
    $finalColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "最终用户表字段:\n";
    foreach ($finalColumns as $column) {
        echo "  - {$column['name']} ({$column['type']})\n";
    }
    
    // 检查是否有用户数据
    $stmt = $pdo->query("SELECT COUNT(*) FROM bbs_user");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount == 0) {
        echo "\n8. 创建默认管理员账户...\n";
        
        $username = 'admin';
        $email = '<EMAIL>';
        $password = 'admin123456';
        $time = time();
        
        // 使用简单的MD5哈希（临时）
        $password_hash = md5($password);
        
        $sql = "INSERT INTO bbs_user (username, email, password, salt, gid, create_date, create_ip, login_date, login_ip, logins) 
                VALUES (?, ?, ?, '', 1, ?, 0, ?, 0, 1)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([$username, $email, $password_hash, $time, $time]);
        
        if ($result) {
            echo "  ✓ 创建默认管理员账户成功\n";
            echo "    用户名: $username\n";
            echo "    邮箱: $email\n";
            echo "    密码: $password\n";
            echo "    请登录后立即修改密码！\n";
        } else {
            echo "  ✗ 创建默认管理员账户失败\n";
        }
    } else {
        echo "\n8. 数据库中已有 $userCount 个用户\n";
    }
    
    echo "\n✅ 数据库结构修复完成！\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库操作失败: " . $e->getMessage() . "\n";
}

function createMissingTable($pdo, $tableName) {
    $schemas = [
        'bbs_session' => "CREATE TABLE bbs_session (
            sid VARCHAR(32) PRIMARY KEY,
            uid INTEGER DEFAULT 0,
            ip INTEGER DEFAULT 0,
            create_date INTEGER DEFAULT 0,
            last_date INTEGER DEFAULT 0,
            data TEXT DEFAULT ''
        )",
        'bbs_config' => "CREATE TABLE bbs_config (
            k VARCHAR(32) PRIMARY KEY,
            v TEXT NOT NULL,
            expiry INTEGER DEFAULT 0
        )"
    ];
    
    if (isset($schemas[$tableName])) {
        try {
            $pdo->exec($schemas[$tableName]);
            echo "  ✓ 创建表 $tableName 成功\n";
        } catch (PDOException $e) {
            echo "  ✗ 创建表 $tableName 失败: " . $e->getMessage() . "\n";
        }
    }
}

echo "\n=== 数据库结构修复完成 ===\n";

?>
