<?php

!defined('DEBUG') AND exit('Access Denied.');

$action = param(1);

// hook my_start.php

$user = user_read($uid);
user_login_check();

$header['mobile_title'] = $user['username'];
$header['mobile_linke'] = url("my");

is_numeric($action) AND $action = '';

$active = $action;

// hook my_action_before.php

if(empty($action)) {
	
	$header['title'] = lang('my_home');
	
	
	
	include _include(APP_PATH.'view/htm/my.htm');
	
/*	
} elseif($action == 'profile') {
	
	if($ajax) {
		// user_safe_info($user);
		message(0, $user);
	} else {
		include _include(APP_PATH.'view/htm/my_profile.htm');
	}
*/
	
} elseif($action == 'password') {
	
	if($method == 'GET') {
		
		// hook my_password_get_start.php
		
		include _include(APP_PATH.'view/htm/my_password.htm');
		
	} elseif($method == 'POST') {

		// hook my_password_post_start.php

		// CSRF 保护
		csrf_token_check();

		$password_old = param('password_old');
		$password_new = param('password_new');
		$password_new_repeat = param('password_new_repeat');
		$password_new_repeat != $password_new AND message(-1, lang('repeat_password_incorrect'));

		// 使用新的密码验证函数
		!user_password_verify($password_old, $user['password'], $user['salt']) AND message('password_old', lang('old_password_incorrect'));

		// 检查新密码强度
		!is_password_strong($password_new, $err) AND message('password_new', $err);

		// 使用新的密码哈希
		$password_new_hash = user_password_hash($password_new);
		$r = user_update($uid, array('password'=>$password_new_hash, 'salt'=>''));
		$r === FALSE AND message(-1, lang('password_modify_failed'));
		
		// hook my_password_post_end.php
		message(0, lang('password_modify_successfully'));
		
	}
	

} elseif($action == 'thread') {

	// hook my_thread_start.php
	
	$page = param(2, 1);
	$pagesize = 20;
	$totalnum = $user['threads'];
	
	// hook my_profile_thread_list_before.php
	
	$pagination = pagination(url('my-thread-{page}'), $totalnum, $page, $pagesize);
	$threadlist = mythread_find_by_uid($uid, $page, $pagesize);
	
	// hook my_thread_end.php
	
	include _include(APP_PATH.'view/htm/my_thread.htm');

	
} elseif($action == 'avatar') {
	
	if($method == 'GET') {
		
		// hook my_avatar_get_start.php
		
		include _include(APP_PATH.'view/htm/my_avatar.htm');
	
	} else {
		
		// hook my_avatar_post_start.php
		
		$width = param('width');
		$height = param('height');
		$data = param('data', '', FALSE);
		
		empty($data) AND message(-1, lang('data_is_empty'));
		$data = base64_decode_file_data($data);
		$size = strlen($data);
		$size > 40000 AND message(-1, lang('filesize_too_large', array('maxsize'=>'40K', 'size'=>$size)));
		
		$filename = "$uid.png";
		$dir = substr(sprintf("%09d", $uid), 0, 3).'/';
		$path = $conf['upload_path'].'avatar/'.$dir;
		$url = $conf['upload_url'].'avatar/'.$dir.$filename;
		!is_dir($path) AND (mkdir($path, 0777, TRUE) OR message(-2, lang('directory_create_failed')));
		
		// hook my_avatar_post_save_before.php
		file_put_contents($path.$filename, $data) OR message(-1, lang('write_to_file_failed'));
		
		user_update($uid, array('avatar'=>$time));
		
		// hook my_avatar_post_end.php
		
		message(0, array('url'=>$url));
		
	}
}

// hook my_end.php

?>