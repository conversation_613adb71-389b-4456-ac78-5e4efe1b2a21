<?php

/*
 * <PERSON><PERSON> BBS 6.1 安装程序 - 安装完成
 */

// 检查安装锁文件
if (!file_exists('../data/install.lock')) {
    header('Location: index.php');
    exit;
}

$install_time = file_get_contents('../data/install.lock');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boyou BBS 6.1 安装完成</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 3em;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 1.2em;
        }
        .content {
            padding: 40px;
        }
        .success-icon {
            text-align: center;
            font-size: 5em;
            color: #28a745;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #28a745;
        }
        .info-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.3em;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
            background: #d4edda;
            color: #155724;
        }
        .btn {
            display: inline-block;
            padding: 15px 35px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-weight: bold;
            font-size: 1.1em;
            transition: transform 0.2s;
            margin: 10px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .security-notice h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        .security-notice ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            color: #666;
        }
        .next-steps {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
        }
        .next-steps h3 {
            margin: 0 0 15px 0;
            color: #1976d2;
        }
        .next-steps ul {
            margin: 0;
            padding-left: 20px;
        }
        .next-steps li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 安装成功！</h1>
            <p>Boyou BBS 6.1 已成功安装</p>
        </div>
        
        <div class="content">
            <div class="success-icon">
                ✅
            </div>
            
            <div style="text-align: center; margin-bottom: 30px;">
                <h2>恭喜！Boyou BBS 6.1 安装完成</h2>
                <p>您的现代化论坛系统已经准备就绪</p>
            </div>
            
            <div class="info-grid">
                <div class="info-card">
                    <h3>📊 安装信息</h3>
                    <div class="info-item">
                        <span>安装时间</span>
                        <span class="badge"><?php echo $install_time; ?></span>
                    </div>
                    <div class="info-item">
                        <span>系统版本</span>
                        <span class="badge">Boyou BBS 6.1.0</span>
                    </div>
                    <div class="info-item">
                        <span>框架版本</span>
                        <span class="badge">BoyouPHP 6.1</span>
                    </div>
                    <div class="info-item">
                        <span>PHP版本</span>
                        <span class="badge"><?php echo PHP_VERSION; ?></span>
                    </div>
                </div>
                
                <div class="info-card">
                    <h3>🛡️ 安全功能</h3>
                    <div class="info-item">
                        <span>CSRF保护</span>
                        <span class="badge">✓ 已启用</span>
                    </div>
                    <div class="info-item">
                        <span>XSS防护</span>
                        <span class="badge">✓ 已启用</span>
                    </div>
                    <div class="info-item">
                        <span>输入过滤</span>
                        <span class="badge">✓ 已启用</span>
                    </div>
                    <div class="info-item">
                        <span>安全等级</span>
                        <span class="badge">A级</span>
                    </div>
                </div>
            </div>
            
            <div class="security-notice">
                <h4>🔒 重要安全提醒</h4>
                <p>为了确保您的论坛安全，请立即执行以下操作：</p>
                <ul>
                    <li><strong>删除安装目录</strong>：删除 <code>install/</code> 目录以防止重复安装</li>
                    <li><strong>修改默认密码</strong>：登录后立即修改管理员密码</li>
                    <li><strong>配置HTTPS</strong>：在生产环境中启用HTTPS加密</li>
                    <li><strong>设置文件权限</strong>：确保敏感文件的权限设置正确</li>
                    <li><strong>定期备份</strong>：设置自动备份计划</li>
                </ul>
            </div>
            
            <div class="next-steps">
                <h3>🚀 下一步操作</h3>
                <ul>
                    <li>访问论坛首页，体验全新的界面设计</li>
                    <li>登录管理后台，配置论坛基本设置</li>
                    <li>创建版块，设置版块权限</li>
                    <li>安装和配置插件</li>
                    <li>自定义主题和样式</li>
                    <li>配置邮件发送功能</li>
                    <li>设置SEO优化选项</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="../" class="btn btn-success">🏠 访问论坛首页</a>
                <a href="../admin/" class="btn">⚙️ 进入管理后台</a>
                <a href="../health_check.php" class="btn btn-secondary">🏥 系统健康检查</a>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>Boyou BBS 6.1</strong> &copy; 2024 | 基于 BoyouPHP 6.1 框架</p>
            <p>现代化论坛系统，为社区交流而生</p>
            <p style="margin-top: 15px; font-size: 0.9em;">
                🌟 感谢选择 Boyou BBS！如有问题，请查看文档或联系技术支持。
            </p>
        </div>
    </div>
</body>
</html>
