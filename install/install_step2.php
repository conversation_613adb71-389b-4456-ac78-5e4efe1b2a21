<?php

/*
 * Boyou BBS 6.1 安装程序 - 步骤2: 数据库配置
 */

// 检查是否已安装
if (file_exists('../data/install.lock')) {
    header('Location: ../');
    exit;
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_port = $_POST['db_port'] ?? '3306';
    $db_name = $_POST['db_name'] ?? 'boyou_bbs';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    $db_prefix = $_POST['db_prefix'] ?? 'bbs_';
    
    $admin_username = $_POST['admin_username'] ?? 'admin';
    $admin_password = $_POST['admin_password'] ?? '';
    $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
    $site_name = $_POST['site_name'] ?? 'Boyou BBS';
    $site_url = $_POST['site_url'] ?? 'http://localhost:8000';
    
    // 验证必填字段
    $errors = [];
    if (empty($db_name)) $errors[] = '数据库名不能为空';
    if (empty($admin_username)) $errors[] = '管理员用户名不能为空';
    if (empty($admin_password)) $errors[] = '管理员密码不能为空';
    if (strlen($admin_password) < 6) $errors[] = '管理员密码至少6位';
    if (empty($admin_email)) $errors[] = '管理员邮箱不能为空';
    
    if (empty($errors)) {
        // 测试数据库连接
        try {
            $dsn = "mysql:host=$db_host;port=$db_port;charset=utf8mb4";
            $pdo = new PDO($dsn, $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 创建数据库（如果不存在）
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `$db_name`");
            
            // 创建配置文件
            $config_content = "<?php\n\n";
            $config_content .= "// Boyou BBS 6.1 配置文件\n";
            $config_content .= "// 自动生成于 " . date('Y-m-d H:i:s') . "\n\n";
            $config_content .= "\$conf = [];\n\n";
            $config_content .= "// 站点信息\n";
            $config_content .= "\$conf['sitename'] = '" . addslashes($site_name) . "';\n";
            $config_content .= "\$conf['siteurl'] = '" . addslashes($site_url) . "';\n";
            $config_content .= "\$conf['version'] = '6.1.0';\n\n";
            $config_content .= "// 数据库配置\n";
            $config_content .= "\$conf['db']['mysql']['master']['host'] = '" . addslashes($db_host) . "';\n";
            $config_content .= "\$conf['db']['mysql']['master']['port'] = '" . addslashes($db_port) . "';\n";
            $config_content .= "\$conf['db']['mysql']['master']['name'] = '" . addslashes($db_name) . "';\n";
            $config_content .= "\$conf['db']['mysql']['master']['user'] = '" . addslashes($db_user) . "';\n";
            $config_content .= "\$conf['db']['mysql']['master']['password'] = '" . addslashes($db_pass) . "';\n";
            $config_content .= "\$conf['db']['mysql']['master']['charset'] = 'utf8mb4';\n";
            $config_content .= "\$conf['db']['mysql']['master']['engine'] = 'InnoDB';\n";
            $config_content .= "\$conf['db']['tablepre'] = '" . addslashes($db_prefix) . "';\n\n";
            $config_content .= "// 缓存配置\n";
            $config_content .= "\$conf['cache']['type'] = 'file';\n";
            $config_content .= "\$conf['cache']['dir'] = './tmp/';\n\n";
            $config_content .= "// 其他配置\n";
            $config_content .= "\$conf['tmp_path'] = './tmp/';\n";
            $config_content .= "\$conf['upload_path'] = './upload/';\n";
            $config_content .= "\$conf['log_path'] = './log/';\n\n";
            $config_content .= "return \$conf;\n";
            $config_content .= "?>";
            
            file_put_contents('../conf/conf.php', $config_content);
            
            // 创建数据库表
            $sql_tables = "
            CREATE TABLE IF NOT EXISTS `{$db_prefix}user` (
                `uid` int(11) NOT NULL AUTO_INCREMENT,
                `username` varchar(32) NOT NULL,
                `password` varchar(64) NOT NULL,
                `email` varchar(64) NOT NULL,
                `create_date` int(11) NOT NULL,
                `login_date` int(11) NOT NULL,
                `group_id` int(11) NOT NULL DEFAULT 1,
                PRIMARY KEY (`uid`),
                UNIQUE KEY `username` (`username`),
                UNIQUE KEY `email` (`email`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            
            CREATE TABLE IF NOT EXISTS `{$db_prefix}group` (
                `group_id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(32) NOT NULL,
                `credit_from` int(11) NOT NULL DEFAULT 0,
                `credit_to` int(11) NOT NULL DEFAULT 0,
                PRIMARY KEY (`group_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            
            CREATE TABLE IF NOT EXISTS `{$db_prefix}forum` (
                `fid` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(64) NOT NULL,
                `rank` int(11) NOT NULL DEFAULT 0,
                `threads` int(11) NOT NULL DEFAULT 0,
                `posts` int(11) NOT NULL DEFAULT 0,
                PRIMARY KEY (`fid`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            
            CREATE TABLE IF NOT EXISTS `{$db_prefix}thread` (
                `tid` int(11) NOT NULL AUTO_INCREMENT,
                `fid` int(11) NOT NULL,
                `uid` int(11) NOT NULL,
                `subject` varchar(128) NOT NULL,
                `create_date` int(11) NOT NULL,
                `last_date` int(11) NOT NULL,
                `posts` int(11) NOT NULL DEFAULT 1,
                `views` int(11) NOT NULL DEFAULT 0,
                PRIMARY KEY (`tid`),
                KEY `fid` (`fid`),
                KEY `uid` (`uid`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            
            CREATE TABLE IF NOT EXISTS `{$db_prefix}post` (
                `pid` int(11) NOT NULL AUTO_INCREMENT,
                `tid` int(11) NOT NULL,
                `uid` int(11) NOT NULL,
                `create_date` int(11) NOT NULL,
                `message` text NOT NULL,
                PRIMARY KEY (`pid`),
                KEY `tid` (`tid`),
                KEY `uid` (`uid`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ";
            
            $pdo->exec($sql_tables);
            
            // 插入默认数据
            $pdo->exec("INSERT IGNORE INTO `{$db_prefix}group` VALUES (1, '管理员', 0, 999999), (2, '版主', 100, 999999), (3, '会员', 0, 99)");
            $pdo->exec("INSERT IGNORE INTO `{$db_prefix}forum` VALUES (1, '默认版块', 1, 0, 0)");
            
            // 创建管理员账户
            $password_hash = password_hash($admin_password, PASSWORD_DEFAULT);
            $create_time = time();
            $stmt = $pdo->prepare("INSERT IGNORE INTO `{$db_prefix}user` (username, password, email, create_date, login_date, group_id) VALUES (?, ?, ?, ?, ?, 1)");
            $stmt->execute([$admin_username, $password_hash, $admin_email, $create_time, $create_time]);
            
            // 创建安装锁文件
            file_put_contents('../data/install.lock', date('Y-m-d H:i:s'));
            
            // 重定向到安装完成页面
            header('Location: install_complete.php');
            exit;
            
        } catch (Exception $e) {
            $errors[] = '数据库连接失败: ' . $e->getMessage();
        }
    }
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boyou BBS 6.1 安装向导 - 数据库配置</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .content {
            padding: 40px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        }
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .form-section h3 {
            margin: 0 0 20px 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #6c757d;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .row {
            display: flex;
            gap: 20px;
        }
        .col {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Boyou BBS 6.1</h1>
            <p>安装向导 - 步骤 2/3</p>
        </div>
        
        <div class="content">
            <?php if (!empty($errors)): ?>
                <div class="error">
                    <strong>安装失败：</strong><br>
                    <?php foreach ($errors as $error): ?>
                        • <?php echo htmlspecialchars($error); ?><br>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <form method="post">
                <div class="form-section">
                    <h3>📊 数据库配置</h3>
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label for="db_host">数据库主机</label>
                                <input type="text" id="db_host" name="db_host" value="<?php echo htmlspecialchars($_POST['db_host'] ?? 'localhost'); ?>" required>
                                <div class="help-text">通常为 localhost 或 127.0.0.1</div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label for="db_port">端口</label>
                                <input type="text" id="db_port" name="db_port" value="<?php echo htmlspecialchars($_POST['db_port'] ?? '3306'); ?>" required>
                                <div class="help-text">MySQL默认端口为 3306</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_name">数据库名</label>
                        <input type="text" id="db_name" name="db_name" value="<?php echo htmlspecialchars($_POST['db_name'] ?? 'boyou_bbs'); ?>" required>
                        <div class="help-text">如果数据库不存在，系统会自动创建</div>
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label for="db_user">数据库用户名</label>
                                <input type="text" id="db_user" name="db_user" value="<?php echo htmlspecialchars($_POST['db_user'] ?? 'root'); ?>" required>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label for="db_pass">数据库密码</label>
                                <input type="password" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($_POST['db_pass'] ?? ''); ?>">
                                <div class="help-text">如果没有密码请留空</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_prefix">表前缀</label>
                        <input type="text" id="db_prefix" name="db_prefix" value="<?php echo htmlspecialchars($_POST['db_prefix'] ?? 'bbs_'); ?>" required>
                        <div class="help-text">建议使用 bbs_ 作为表前缀</div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h3>👤 管理员账户</h3>
                    <div class="form-group">
                        <label for="admin_username">管理员用户名</label>
                        <input type="text" id="admin_username" name="admin_username" value="<?php echo htmlspecialchars($_POST['admin_username'] ?? 'admin'); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_password">管理员密码</label>
                        <input type="password" id="admin_password" name="admin_password" value="<?php echo htmlspecialchars($_POST['admin_password'] ?? ''); ?>" required>
                        <div class="help-text">密码至少6位字符</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_email">管理员邮箱</label>
                        <input type="email" id="admin_email" name="admin_email" value="<?php echo htmlspecialchars($_POST['admin_email'] ?? '<EMAIL>'); ?>" required>
                    </div>
                </div>
                
                <div class="form-section">
                    <h3>🌐 站点配置</h3>
                    <div class="form-group">
                        <label for="site_name">站点名称</label>
                        <input type="text" id="site_name" name="site_name" value="<?php echo htmlspecialchars($_POST['site_name'] ?? 'Boyou BBS'); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="site_url">站点URL</label>
                        <input type="url" id="site_url" name="site_url" value="<?php echo htmlspecialchars($_POST['site_url'] ?? 'http://localhost:8000'); ?>" required>
                        <div class="help-text">请输入完整的网站地址，包括 http:// 或 https://</div>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 40px;">
                    <a href="index.php" class="btn btn-secondary">← 上一步</a>
                    <button type="submit" class="btn">开始安装 →</button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
