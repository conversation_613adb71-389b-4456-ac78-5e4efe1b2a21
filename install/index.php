<?php

/*
 * Boyou BBS 6.1 安装程序
 */

// 检查是否已安装
if (file_exists('../data/install.lock')) {
    echo '<h1>Boyou BBS 已经安装</h1>';
    echo '<p>如果需要重新安装，请删除 data/install.lock 文件</p>';
    echo '<p><a href="../">访问首页</a></p>';
    exit;
}

// 简化的配置
$conf = [
    'sitename' => 'Boyou BBS',
    'version' => '6.1.0',
    'tmp_path' => '../tmp/'
];

// 简化的语言配置
$lang = [
    'install_title' => 'Boyou BBS 6.1 安装向导',
    'install_welcome' => '欢迎使用 Boyou BBS 6.1'
];

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boyou BBS 6.1 安装向导</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Aria<PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .step h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.3em;
        }
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .check-item:last-child {
            border-bottom: none;
        }
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .status.ok {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Boyou BBS 6.1</h1>
            <p>现代化论坛系统安装向导</p>
        </div>

        <div class="content">
            <div class="step">
                <h3>📋 系统环境检查</h3>

                <?php
                $checks = [
                    'PHP版本 >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
                    'PDO扩展' => extension_loaded('pdo'),
                    'JSON扩展' => extension_loaded('json'),
                    'mbstring扩展' => extension_loaded('mbstring'),
                    'GD扩展' => extension_loaded('gd'),
                    'upload目录可写' => is_writable('../upload'),
                    'tmp目录可写' => is_writable('../tmp'),
                    'log目录可写' => is_writable('../log'),
                    'data目录可写' => is_writable('../data')
                ];

                $allPassed = true;
                foreach ($checks as $item => $status) {
                    $allPassed = $allPassed && $status;
                    $statusClass = $status ? 'ok' : 'error';
                    $statusText = $status ? '✓ 通过' : '✗ 失败';

                    echo "<div class='check-item'>";
                    echo "<span>$item</span>";
                    echo "<span class='status $statusClass'>$statusText</span>";
                    echo "</div>";
                }
                ?>
            </div>

            <div class="step">
                <h3>🔧 框架信息</h3>
                <div class="check-item">
                    <span>BoyouPHP版本</span>
                    <span class="status ok"><?php echo defined('BOYOUPHP_VERSION') ? BOYOUPHP_VERSION : '6.1'; ?></span>
                </div>
                <div class="check-item">
                    <span>PHP版本</span>
                    <span class="status ok"><?php echo PHP_VERSION; ?></span>
                </div>
                <div class="check-item">
                    <span>服务器软件</span>
                    <span class="status ok"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></span>
                </div>
            </div>

            <div class="step">
                <h3>📝 安装说明</h3>
                <p>欢迎使用 Boyou BBS 6.1！这是一个现代化的论坛系统，基于 BoyouPHP 6.1 框架开发。</p>
                <ul>
                    <li>✨ 现代化的用户界面设计</li>
                    <li>🛡️ 企业级安全防护</li>
                    <li>⚡ 高性能缓存系统</li>
                    <li>📱 完美的移动端适配</li>
                    <li>🔧 强大的插件系统</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <?php if ($allPassed): ?>
                    <a href="install_step2.php" class="btn">开始安装 →</a>
                <?php else: ?>
                    <button class="btn" disabled>请先解决环境问题</button>
                <?php endif; ?>
            </div>
        </div>

        <div class="footer">
            <p>Boyou BBS 6.1 &copy; 2024 | 基于 BoyouPHP 6.1 框架</p>
        </div>
    </div>
</body>
</html>