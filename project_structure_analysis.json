{"directories": {"/": {"exists": true, "description": "项目根目录", "file_count": 299}, "/admin/": {"exists": true, "description": "管理后台目录", "file_count": 33}, "/admin/route/": {"exists": true, "description": "管理后台路由", "file_count": 8}, "/admin/view/": {"exists": true, "description": "管理后台模板", "file_count": 21}, "/boyouphp/": {"exists": true, "description": "BoyouPHP框架核心", "file_count": 33}, "/conf/": {"exists": true, "description": "配置文件目录", "file_count": 5}, "/data/": {"exists": true, "description": "数据存储目录", "file_count": 2}, "/install/": {"exists": true, "description": "安装程序目录", "file_count": 15}, "/lang/": {"exists": true, "description": "多语言文件", "file_count": 20}, "/log/": {"exists": true, "description": "日志文件目录", "file_count": 3}, "/model/": {"exists": true, "description": "数据模型目录", "file_count": 21}, "/plugin/": {"exists": true, "description": "插件目录", "file_count": 0}, "/route/": {"exists": true, "description": "前台路由目录", "file_count": 9}, "/tmp/": {"exists": true, "description": "临时文件目录", "file_count": 39}, "/tool/": {"exists": true, "description": "工具脚本目录", "file_count": 14}, "/upload/": {"exists": true, "description": "上传文件目录", "file_count": 4}, "/view/": {"exists": true, "description": "前台模板目录", "file_count": 69}, "/view/css/": {"exists": true, "description": "样式文件", "file_count": 3}, "/view/js/": {"exists": true, "description": "JavaScript文件", "file_count": 16}, "/view/htm/": {"exists": true, "description": "HTML模板文件", "file_count": 33}, "/view/img/": {"exists": true, "description": "图片资源", "file_count": 10}}, "core_files": {"index.php": {"exists": true, "description": "主入口文件 - 处理所有前台请求", "size": 2361, "lines": 62}, "index.inc.php": {"exists": true, "description": "路由分发器 - 根据请求分发到对应路由", "size": 2905, "lines": 84}, "model.inc.php": {"exists": true, "description": "模型加载器 - 加载所有数据模型", "size": 1819, "lines": 86}, "boyouphp/boyouphp.php": {"exists": true, "description": "框架核心 - 提供基础功能和工具函数", "size": 9664, "lines": 262}, "boyouphp/boyouphp.min.php": {"exists": true, "description": "框架核心压缩版 - 生产环境使用", "size": 78574, "lines": 135}, "admin/index.php": {"exists": true, "description": "管理后台入口 - 处理管理后台请求", "size": 2801, "lines": 33}, "admin/admin.func.php": {"exists": true, "description": "管理后台函数库 - 管理相关功能函数", "size": 2377, "lines": 84}}, "architecture": {"pattern": "MVC", "routes": ["attach", "browser", "forum", "index", "mod", "my", "post", "thread", "user"], "models": ["attach", "check", "cron", "form", "forum", "forum_access", "group", "kv", "misc", "modlog", "mythread", "plugin", "post", "queue", "runtime", "session", "smtp", "table_day", "thread", "thread_top", "user"]}, "database": {"type": "SQLite", "file": "data/boyou_bbs.db", "tables": ["bbs_config", "bbs_forum", "bbs_group", "bbs_post", "bbs_session", "bbs_thread", "bbs_user", "sqlite_sequence"], "table_count": 8}, "security": {"CSRF保护": true, "XSS防护": true, "密码哈希": true, "会话安全": true, "文件上传安全": true, "权限控制": true}}