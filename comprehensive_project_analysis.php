<?php

echo "=== Boyou BBS 6.1 完整项目分析 ===\n\n";

class ProjectAnalyzer {
    private $projectRoot;
    private $issues = [];
    private $recommendations = [];
    
    public function __construct($projectRoot = '.') {
        $this->projectRoot = $projectRoot;
    }
    
    public function runCompleteAnalysis() {
        echo "开始完整项目分析...\n\n";
        
        $this->analyzeProjectStructure();
        $this->analyzeConfigurationFiles();
        $this->analyzeDatabaseStructure();
        $this->analyzeSecurityIssues();
        $this->analyzeCodeQuality();
        $this->analyzeUserInterface();
        $this->analyzeFunctionality();
        $this->analyzePerformance();
        
        $this->generateAnalysisReport();
    }
    
    private function analyzeProjectStructure() {
        echo "1. 分析项目结构...\n";
        
        $requiredDirs = [
            'boyouphp' => 'BoyouPHP框架目录',
            'view' => '视图模板目录',
            'data' => '数据目录',
            'conf' => '配置目录',
            'admin' => '管理后台目录',
            'install' => '安装程序目录'
        ];
        
        foreach ($requiredDirs as $dir => $description) {
            if (is_dir($dir)) {
                echo "  ✓ $description 存在\n";
            } else {
                $this->addIssue('项目结构', "缺少$description ($dir)", '高');
            }
        }
        
        // 检查核心文件
        $coreFiles = [
            'index.php' => '主入口文件',
            'user.php' => '用户系统文件',
            'forum.php' => '论坛文件',
            'thread.php' => '主题文件',
            'post.php' => '帖子文件'
        ];
        
        foreach ($coreFiles as $file => $description) {
            if (file_exists($file)) {
                echo "  ✓ $description 存在\n";
            } else {
                $this->addIssue('核心文件', "缺少$description ($file)", '高');
            }
        }
        
        echo "\n";
    }
    
    private function analyzeConfigurationFiles() {
        echo "2. 分析配置文件...\n";
        
        // 检查主配置文件
        if (file_exists('conf/conf.php')) {
            $conf = include 'conf/conf.php';
            if (is_array($conf)) {
                echo "  ✓ 主配置文件格式正确\n";
                
                // 检查关键配置项
                $requiredConfigs = [
                    'sitename' => '站点名称',
                    'siteurl' => '站点URL',
                    'db' => '数据库配置',
                    'security_key' => '安全密钥'
                ];
                
                foreach ($requiredConfigs as $key => $description) {
                    if (isset($conf[$key])) {
                        echo "  ✓ $description 已配置\n";
                    } else {
                        $this->addIssue('配置文件', "缺少$description配置 ($key)", '中');
                    }
                }
                
                // 检查安全配置
                if (isset($conf['security_key']) && strlen($conf['security_key']) < 32) {
                    $this->addIssue('安全配置', '安全密钥长度不足', '高');
                }
                
            } else {
                $this->addIssue('配置文件', '主配置文件格式错误', '高');
            }
        } else {
            $this->addIssue('配置文件', '缺少主配置文件', '高');
        }
        
        // 检查数据库配置
        if (file_exists('data/boyou_bbs.db')) {
            echo "  ✓ SQLite数据库文件存在\n";
        } else {
            $this->addIssue('数据库', 'SQLite数据库文件不存在', '高');
        }
        
        echo "\n";
    }
    
    private function analyzeDatabaseStructure() {
        echo "3. 分析数据库结构...\n";
        
        try {
            $pdo = new PDO('sqlite:data/boyou_bbs.db');
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 获取所有表
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "  ✓ 数据库连接成功，包含 " . count($tables) . " 个表\n";
            
            // 检查核心表
            $requiredTables = [
                'bbs_user' => '用户表',
                'bbs_forum' => '版块表',
                'bbs_thread' => '主题表',
                'bbs_post' => '帖子表',
                'bbs_config' => '配置表'
            ];
            
            foreach ($requiredTables as $table => $description) {
                if (in_array($table, $tables)) {
                    echo "  ✓ $description 存在\n";
                    
                    // 检查表结构
                    $this->checkTableStructure($pdo, $table);
                } else {
                    $this->addIssue('数据库结构', "缺少$description ($table)", '高');
                }
            }
            
        } catch (PDOException $e) {
            $this->addIssue('数据库', '数据库连接失败: ' . $e->getMessage(), '高');
        }
        
        echo "\n";
    }
    
    private function checkTableStructure($pdo, $tableName) {
        try {
            $stmt = $pdo->query("PRAGMA table_info($tableName)");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($columns)) {
                $this->addIssue('数据库结构', "$tableName 表结构为空", '中');
                return;
            }
            
            // 检查特定表的必要字段
            switch ($tableName) {
                case 'bbs_user':
                    $requiredColumns = ['uid', 'username', 'password', 'email'];
                    break;
                case 'bbs_forum':
                    $requiredColumns = ['fid', 'name', 'threads', 'posts'];
                    break;
                case 'bbs_thread':
                    $requiredColumns = ['tid', 'fid', 'uid', 'subject'];
                    break;
                case 'bbs_post':
                    $requiredColumns = ['pid', 'tid', 'uid', 'message'];
                    break;
                default:
                    return;
            }
            
            $existingColumns = array_column($columns, 'name');
            foreach ($requiredColumns as $column) {
                if (!in_array($column, $existingColumns)) {
                    $this->addIssue('数据库结构', "$tableName 表缺少 $column 字段", '中');
                }
            }
            
        } catch (PDOException $e) {
            $this->addIssue('数据库结构', "检查 $tableName 表结构失败", '低');
        }
    }
    
    private function analyzeSecurityIssues() {
        echo "4. 分析安全问题...\n";
        
        // 检查文件权限
        $sensitiveFiles = [
            'conf/conf.php' => '配置文件',
            'data/boyou_bbs.db' => '数据库文件',
            'install/' => '安装目录'
        ];
        
        foreach ($sensitiveFiles as $file => $description) {
            if (file_exists($file)) {
                $perms = fileperms($file);
                if ($perms & 0x0004) { // 其他用户可读
                    $this->addIssue('文件权限', "$description 对其他用户可读", '中');
                }
            }
        }
        
        // 检查SQL注入防护
        $this->checkSQLInjectionProtection();
        
        // 检查XSS防护
        $this->checkXSSProtection();
        
        // 检查CSRF防护
        $this->checkCSRFProtection();
        
        echo "\n";
    }
    
    private function checkSQLInjectionProtection() {
        $phpFiles = $this->getPhpFiles();
        $vulnerablePatterns = [
            '/mysql_query\s*\(\s*["\'].*\$.*["\']/' => 'MySQL查询可能存在SQL注入',
            '/\$_GET\[.*\].*mysql_query/' => 'GET参数直接用于数据库查询',
            '/\$_POST\[.*\].*mysql_query/' => 'POST参数直接用于数据库查询'
        ];
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            foreach ($vulnerablePatterns as $pattern => $description) {
                if (preg_match($pattern, $content)) {
                    $this->addIssue('SQL注入', "$file: $description", '高');
                }
            }
        }
    }
    
    private function checkXSSProtection() {
        $phpFiles = $this->getPhpFiles();
        $vulnerablePatterns = [
            '/echo\s+\$_GET\[/' => '直接输出GET参数',
            '/echo\s+\$_POST\[/' => '直接输出POST参数',
            '/print\s+\$_REQUEST\[/' => '直接输出REQUEST参数'
        ];
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            foreach ($vulnerablePatterns as $pattern => $description) {
                if (preg_match($pattern, $content)) {
                    $this->addIssue('XSS漏洞', "$file: $description", '高');
                }
            }
        }
    }
    
    private function checkCSRFProtection() {
        // 检查是否有CSRF token机制
        $hasCSRFProtection = false;
        $phpFiles = $this->getPhpFiles();
        
        foreach ($phpFiles as $file) {
            $content = file_get_contents($file);
            if (strpos($content, 'csrf') !== false || strpos($content, 'token') !== false) {
                $hasCSRFProtection = true;
                break;
            }
        }
        
        if (!$hasCSRFProtection) {
            $this->addIssue('CSRF防护', '缺少CSRF防护机制', '中');
        }
    }
    
    private function analyzeCodeQuality() {
        echo "5. 分析代码质量...\n";
        
        $phpFiles = $this->getPhpFiles();
        
        foreach ($phpFiles as $file) {
            $this->analyzePhpFile($file);
        }
        
        echo "\n";
    }
    
    private function analyzePhpFile($file) {
        $content = file_get_contents($file);
        
        // 检查过时的PHP语法
        $deprecatedPatterns = [
            '/mysql_/' => '使用了过时的mysql_函数',
            '/ereg/' => '使用了过时的ereg函数',
            '/split\s*\(/' => '使用了过时的split函数',
            '/\$HTTP_/' => '使用了过时的$HTTP_全局变量'
        ];
        
        foreach ($deprecatedPatterns as $pattern => $description) {
            if (preg_match($pattern, $content)) {
                $this->addIssue('代码质量', "$file: $description", '中');
            }
        }
        
        // 检查错误处理
        if (strpos($content, 'try') === false && strpos($content, 'catch') === false) {
            if (strpos($content, 'mysql_') !== false || strpos($content, 'mysqli_') !== false) {
                $this->addIssue('错误处理', "$file: 缺少数据库操作错误处理", '低');
            }
        }
    }
    
    private function analyzeUserInterface() {
        echo "6. 分析用户界面...\n";
        
        // 检查模板文件
        if (is_dir('view')) {
            $templateFiles = glob('view/*.htm');
            echo "  ✓ 找到 " . count($templateFiles) . " 个模板文件\n";
            
            foreach ($templateFiles as $template) {
                $this->analyzeTemplate($template);
            }
        } else {
            $this->addIssue('用户界面', '缺少view模板目录', '高');
        }
        
        // 检查静态资源
        $this->analyzeStaticResources();
        
        echo "\n";
    }
    
    private function analyzeTemplate($templateFile) {
        $content = file_get_contents($templateFile);
        
        // 检查基本HTML结构
        if (strpos($content, '<!DOCTYPE') === false) {
            $this->addIssue('模板质量', "$templateFile: 缺少DOCTYPE声明", '低');
        }
        
        if (strpos($content, 'charset') === false) {
            $this->addIssue('模板质量', "$templateFile: 缺少字符集声明", '低');
        }
        
        // 检查XSS防护
        if (preg_match('/\{\$[^}]+\}/', $content)) {
            $this->addIssue('模板安全', "$templateFile: 模板变量可能需要转义", '中');
        }
    }
    
    private function analyzeStaticResources() {
        $resourceDirs = ['css', 'js', 'img', 'view/css', 'view/js', 'view/img'];
        
        foreach ($resourceDirs as $dir) {
            if (is_dir($dir)) {
                echo "  ✓ 静态资源目录 $dir 存在\n";
            }
        }
        
        // 检查关键资源文件
        $keyResources = [
            'css/bootstrap.css' => 'Bootstrap CSS',
            'js/jquery-3.1.0.js' => 'jQuery库',
            'js/boyou.js' => 'Boyou框架JS'
        ];
        
        foreach ($keyResources as $file => $description) {
            if (file_exists($file)) {
                echo "  ✓ $description 存在\n";
            } else {
                $this->addIssue('静态资源', "缺少$description ($file)", '中');
            }
        }
    }
    
    private function analyzeFunctionality() {
        echo "7. 分析功能逻辑...\n";
        
        // 检查用户系统
        $this->analyzeFunctionFile('user.php', '用户系统');
        
        // 检查论坛系统
        $this->analyzeFunctionFile('forum.php', '论坛系统');
        $this->analyzeFunctionFile('thread.php', '主题系统');
        $this->analyzeFunctionFile('post.php', '帖子系统');
        
        // 检查管理系统
        if (is_dir('admin')) {
            $adminFiles = glob('admin/*.php');
            echo "  ✓ 管理后台包含 " . count($adminFiles) . " 个文件\n";
        }
        
        echo "\n";
    }
    
    private function analyzeFunctionFile($file, $description) {
        if (file_exists($file)) {
            echo "  ✓ $description 文件存在\n";
            
            $content = file_get_contents($file);
            
            // 检查基本功能结构
            if (strpos($content, 'action') !== false) {
                echo "  ✓ $description 包含action处理\n";
            } else {
                $this->addIssue('功能逻辑', "$description 缺少action处理机制", '中');
            }
            
        } else {
            $this->addIssue('功能逻辑', "缺少$description ($file)", '高');
        }
    }
    
    private function analyzePerformance() {
        echo "8. 分析性能问题...\n";
        
        // 检查缓存机制
        if (is_dir('tmp') || is_dir('cache')) {
            echo "  ✓ 存在缓存目录\n";
        } else {
            $this->addIssue('性能优化', '缺少缓存目录', '低');
        }
        
        // 检查数据库索引（简单检查）
        try {
            $pdo = new PDO('sqlite:data/boyou_bbs.db');
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='index'");
            $indexes = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (count($indexes) > 0) {
                echo "  ✓ 数据库包含 " . count($indexes) . " 个索引\n";
            } else {
                $this->addIssue('性能优化', '数据库缺少索引', '中');
            }
            
        } catch (PDOException $e) {
            // 数据库连接问题已在前面检查过
        }
        
        echo "\n";
    }
    
    private function getPhpFiles() {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->projectRoot)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }
        
        return $files;
    }
    
    private function addIssue($category, $description, $priority) {
        $this->issues[] = [
            'category' => $category,
            'description' => $description,
            'priority' => $priority
        ];
        
        $icon = $priority === '高' ? '🔴' : ($priority === '中' ? '🟡' : '🟢');
        echo "  $icon [$priority] $description\n";
    }
    
    private function generateAnalysisReport() {
        echo "\n=== 项目分析报告 ===\n\n";
        
        // 按优先级分组问题
        $highPriority = array_filter($this->issues, function($issue) {
            return $issue['priority'] === '高';
        });
        
        $mediumPriority = array_filter($this->issues, function($issue) {
            return $issue['priority'] === '中';
        });
        
        $lowPriority = array_filter($this->issues, function($issue) {
            return $issue['priority'] === '低';
        });
        
        echo "📊 问题统计:\n";
        echo "  🔴 高优先级: " . count($highPriority) . " 个\n";
        echo "  🟡 中优先级: " . count($mediumPriority) . " 个\n";
        echo "  🟢 低优先级: " . count($lowPriority) . " 个\n";
        echo "  📝 总计: " . count($this->issues) . " 个问题\n\n";
        
        // 生成修复建议
        $this->generateRecommendations();
        
        // 保存详细报告
        $reportData = [
            'analysis_time' => date('Y-m-d H:i:s'),
            'total_issues' => count($this->issues),
            'high_priority' => count($highPriority),
            'medium_priority' => count($mediumPriority),
            'low_priority' => count($lowPriority),
            'issues' => $this->issues,
            'recommendations' => $this->recommendations
        ];
        
        file_put_contents('project_analysis_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "📄 详细分析报告已保存到: project_analysis_report.json\n";
    }
    
    private function generateRecommendations() {
        echo "💡 修复建议:\n\n";
        
        // 基于发现的问题生成建议
        $categories = [];
        foreach ($this->issues as $issue) {
            $categories[$issue['category']][] = $issue;
        }
        
        foreach ($categories as $category => $issues) {
            $highCount = count(array_filter($issues, function($i) { return $i['priority'] === '高'; }));
            
            if ($highCount > 0) {
                echo "🔴 $category (高优先级: $highCount 个问题):\n";
                foreach ($issues as $issue) {
                    if ($issue['priority'] === '高') {
                        echo "   • {$issue['description']}\n";
                    }
                }
                echo "\n";
            }
        }
        
        // 通用建议
        echo "🎯 优先修复顺序:\n";
        echo "1. 修复高优先级安全问题\n";
        echo "2. 完善用户系统功能\n";
        echo "3. 加强数据库安全\n";
        echo "4. 优化代码质量\n";
        echo "5. 改进性能表现\n\n";
    }
}

// 主程序
$analyzer = new ProjectAnalyzer();
$analyzer->runCompleteAnalysis();

echo "=== 项目分析完成 ===\n";

?>
