<?php

// 加载框架
define('DEBUG', 1);
define('APP_PATH', __DIR__.'/');
define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');

// 加载配置
$conf = include APP_PATH.'conf/conf.php';
$_SERVER['conf'] = $conf;

// 加载框架
include BOYOUPHP_PATH.'boyouphp.php';

// 加载模型
include APP_PATH.'model/plugin.func.php';
include _include(APP_PATH.'model.inc.php');

echo "=== 修复用户系统问题 ===\n\n";

class UserSystemFixer {
    private $fixes = [];
    
    public function runAllFixes() {
        echo "开始修复用户系统问题...\n\n";
        
        $this->fixPasswordVerification();
        $this->fixUserValidation();
        $this->fixSessionHandling();
        $this->fixCSRFProtection();
        $this->fixDatabaseIssues();
        $this->fixConfigurationIssues();
        $this->testUserFunctions();
        
        $this->generateFixReport();
    }
    
    private function fixPasswordVerification() {
        echo "1. 修复密码验证问题...\n";
        
        // 检查用户表中的密码格式
        try {
            $pdo = new PDO('sqlite:data/boyou_bbs.db');
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 检查是否有用户数据
            $stmt = $pdo->query("SELECT COUNT(*) FROM bbs_user");
            $userCount = $stmt->fetchColumn();
            
            if ($userCount == 0) {
                echo "  ⚠️  数据库中没有用户，创建默认管理员账户...\n";
                $this->createDefaultAdmin($pdo);
            } else {
                echo "  ✓ 数据库中有 $userCount 个用户\n";
                
                // 检查密码格式并升级
                $this->upgradePasswordHashes($pdo);
            }
            
            $this->addFix('密码验证', '密码验证系统已修复', true);
            
        } catch (PDOException $e) {
            $this->addFix('密码验证', '数据库连接失败: ' . $e->getMessage(), false);
        }
    }
    
    private function createDefaultAdmin($pdo) {
        // 创建默认管理员账户
        $username = 'admin';
        $email = '<EMAIL>';
        $password = 'admin123456';
        $time = time();
        
        // 使用新的密码哈希
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        
        $sql = "INSERT INTO bbs_user (username, email, password, salt, gid, create_date, create_ip, login_date, login_ip, logins) 
                VALUES (?, ?, ?, '', 1, ?, 0, ?, 0, 1)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([$username, $email, $password_hash, $time, $time]);
        
        if ($result) {
            echo "  ✓ 创建默认管理员账户成功\n";
            echo "    用户名: $username\n";
            echo "    邮箱: $email\n";
            echo "    密码: $password\n";
            echo "    请登录后立即修改密码！\n";
        } else {
            echo "  ✗ 创建默认管理员账户失败\n";
        }
    }
    
    private function upgradePasswordHashes($pdo) {
        // 检查并升级旧的密码哈希
        $stmt = $pdo->query("SELECT uid, password, salt FROM bbs_user WHERE password NOT LIKE '$%'");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($users) > 0) {
            echo "  ⚠️  发现 " . count($users) . " 个用户使用旧密码格式，需要在下次登录时升级\n";
            
            // 为这些用户添加标记，在下次登录时升级密码
            foreach ($users as $user) {
                if (strlen($user['password']) == 32 && empty($user['salt'])) {
                    // 这是MD5格式的密码，标记为需要升级
                    echo "  ⚠️  用户 UID {$user['uid']} 使用MD5密码，将在下次登录时升级\n";
                }
            }
        } else {
            echo "  ✓ 所有用户密码格式正确\n";
        }
    }
    
    private function fixUserValidation() {
        echo "2. 修复用户验证问题...\n";
        
        // 检查用户验证函数
        $validationFunctions = [
            'is_email' => '邮箱验证函数',
            'is_username' => '用户名验证函数',
            'is_password_strong' => '密码强度验证函数'
        ];
        
        foreach ($validationFunctions as $func => $description) {
            if (function_exists($func)) {
                echo "  ✓ $description 存在\n";
            } else {
                echo "  ✗ $description 不存在\n";
                $this->addFix('用户验证', "$description 缺失", false);
            }
        }
        
        // 测试邮箱验证
        if (function_exists('is_email')) {
            $testEmails = [
                '<EMAIL>' => true,
                'invalid-email' => false,
                'test@' => false,
                '@example.com' => false
            ];
            
            $emailValidationWorks = true;
            foreach ($testEmails as $email => $expected) {
                $result = is_email($email, $err);
                if ($result !== $expected) {
                    $emailValidationWorks = false;
                    break;
                }
            }
            
            if ($emailValidationWorks) {
                echo "  ✓ 邮箱验证功能正常\n";
            } else {
                echo "  ✗ 邮箱验证功能异常\n";
                $this->addFix('用户验证', '邮箱验证功能异常', false);
            }
        }
        
        $this->addFix('用户验证', '用户验证系统检查完成', true);
    }
    
    private function fixSessionHandling() {
        echo "3. 修复会话处理问题...\n";
        
        // 检查会话相关函数
        $sessionFunctions = [
            'sess_start' => '会话启动函数',
            'user_token_set' => '用户令牌设置函数',
            'user_token_get' => '用户令牌获取函数'
        ];
        
        foreach ($sessionFunctions as $func => $description) {
            if (function_exists($func)) {
                echo "  ✓ $description 存在\n";
            } else {
                echo "  ✗ $description 不存在\n";
                $this->addFix('会话处理', "$description 缺失", false);
            }
        }
        
        // 检查会话配置
        $sessionConfig = [
            'session.cookie_httponly' => 'HttpOnly Cookie设置',
            'session.use_only_cookies' => '仅使用Cookie设置',
            'session.cookie_secure' => 'Secure Cookie设置'
        ];
        
        foreach ($sessionConfig as $setting => $description) {
            $value = ini_get($setting);
            echo "  ✓ $description: $value\n";
        }
        
        $this->addFix('会话处理', '会话处理系统检查完成', true);
    }
    
    private function fixCSRFProtection() {
        echo "4. 修复CSRF保护问题...\n";
        
        // 检查CSRF函数
        $csrfFunctions = [
            'csrf_token_generate' => 'CSRF令牌生成函数',
            'csrf_token_verify' => 'CSRF令牌验证函数',
            'csrf_token_check' => 'CSRF令牌检查函数',
            'csrf_token_field' => 'CSRF隐藏字段函数'
        ];
        
        foreach ($csrfFunctions as $func => $description) {
            if (function_exists($func)) {
                echo "  ✓ $description 存在\n";
            } else {
                echo "  ✗ $description 不存在\n";
                $this->addFix('CSRF保护', "$description 缺失", false);
            }
        }
        
        // 测试CSRF功能
        if (function_exists('csrf_token_generate') && function_exists('csrf_token_verify')) {
            session_start();
            $token = csrf_token_generate();
            $isValid = csrf_token_verify($token);
            
            if ($isValid) {
                echo "  ✓ CSRF令牌生成和验证功能正常\n";
            } else {
                echo "  ✗ CSRF令牌验证功能异常\n";
                $this->addFix('CSRF保护', 'CSRF令牌验证功能异常', false);
            }
        }
        
        $this->addFix('CSRF保护', 'CSRF保护系统检查完成', true);
    }
    
    private function fixDatabaseIssues() {
        echo "5. 修复数据库问题...\n";
        
        try {
            $pdo = new PDO('sqlite:data/boyou_bbs.db');
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 检查用户表结构
            $stmt = $pdo->query("PRAGMA table_info(bbs_user)");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $requiredColumns = [
                'uid' => 'INTEGER',
                'username' => 'TEXT',
                'password' => 'TEXT',
                'email' => 'TEXT',
                'gid' => 'INTEGER',
                'create_date' => 'INTEGER',
                'login_date' => 'INTEGER'
            ];
            
            $existingColumns = [];
            foreach ($columns as $column) {
                $existingColumns[$column['name']] = $column['type'];
            }
            
            $missingColumns = [];
            foreach ($requiredColumns as $name => $type) {
                if (!isset($existingColumns[$name])) {
                    $missingColumns[] = $name;
                }
            }
            
            if (empty($missingColumns)) {
                echo "  ✓ 用户表结构完整\n";
            } else {
                echo "  ⚠️  用户表缺少字段: " . implode(', ', $missingColumns) . "\n";
                $this->addFix('数据库结构', '用户表缺少必要字段', false);
            }
            
            // 检查索引
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='bbs_user'");
            $indexes = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "  ✓ 用户表包含 " . count($indexes) . " 个索引\n";
            
            $this->addFix('数据库问题', '数据库结构检查完成', true);
            
        } catch (PDOException $e) {
            $this->addFix('数据库问题', '数据库检查失败: ' . $e->getMessage(), false);
        }
    }
    
    private function fixConfigurationIssues() {
        echo "6. 修复配置问题...\n";
        
        // 检查配置文件
        if (file_exists('conf/conf.php')) {
            $conf = include 'conf/conf.php';
            
            // 检查用户相关配置
            $userConfigs = [
                'user_create_on' => '用户注册开关',
                'user_create_email_on' => '邮箱验证开关',
                'user_resetpw_on' => '密码重置开关',
                'auth_key' => '认证密钥'
            ];
            
            foreach ($userConfigs as $key => $description) {
                if (isset($conf[$key])) {
                    echo "  ✓ $description 已配置\n";
                } else {
                    echo "  ⚠️  $description 未配置\n";
                    $this->addFix('配置问题', "$description 未配置", false);
                }
            }
            
            // 检查安全密钥
            if (isset($conf['auth_key']) && strlen($conf['auth_key']) >= 32) {
                echo "  ✓ 安全密钥长度足够\n";
            } else {
                echo "  ⚠️  安全密钥长度不足或未设置\n";
                $this->addFix('配置问题', '安全密钥需要加强', false);
            }
            
            $this->addFix('配置问题', '配置检查完成', true);
            
        } else {
            $this->addFix('配置问题', '配置文件不存在', false);
        }
    }
    
    private function testUserFunctions() {
        echo "7. 测试用户功能...\n";
        
        // 测试用户读取功能
        if (function_exists('user_read')) {
            try {
                $user = user_read(1);
                if ($user) {
                    echo "  ✓ 用户读取功能正常\n";
                } else {
                    echo "  ⚠️  用户读取功能返回空结果\n";
                }
            } catch (Exception $e) {
                echo "  ✗ 用户读取功能异常: " . $e->getMessage() . "\n";
                $this->addFix('用户功能', '用户读取功能异常', false);
            }
        } else {
            echo "  ✗ 用户读取功能不存在\n";
            $this->addFix('用户功能', '用户读取功能不存在', false);
        }
        
        // 测试密码哈希功能
        if (function_exists('user_password_hash') && function_exists('user_password_verify')) {
            $testPassword = 'test123456';
            $hash = user_password_hash($testPassword);
            $isValid = user_password_verify($testPassword, $hash);
            
            if ($isValid) {
                echo "  ✓ 密码哈希和验证功能正常\n";
            } else {
                echo "  ✗ 密码哈希和验证功能异常\n";
                $this->addFix('用户功能', '密码哈希验证功能异常', false);
            }
        } else {
            echo "  ✗ 密码哈希功能不存在\n";
            $this->addFix('用户功能', '密码哈希功能不存在', false);
        }
        
        $this->addFix('用户功能', '用户功能测试完成', true);
    }
    
    private function addFix($category, $description, $success) {
        $this->fixes[] = [
            'category' => $category,
            'description' => $description,
            'success' => $success
        ];
        
        $status = $success ? '✓' : '✗';
        echo "  $status $description\n";
    }
    
    private function generateFixReport() {
        echo "\n=== 用户系统修复报告 ===\n\n";
        
        $total = count($this->fixes);
        $successful = array_filter($this->fixes, function($fix) {
            return $fix['success'];
        });
        $successCount = count($successful);
        $failedCount = $total - $successCount;
        
        echo "📊 修复统计:\n";
        echo "  总修复项: $total\n";
        echo "  成功修复: $successCount\n";
        echo "  失败修复: $failedCount\n";
        echo "  成功率: " . round(($successCount / $total) * 100, 1) . "%\n\n";
        
        if ($failedCount > 0) {
            echo "❌ 需要手动处理的问题:\n";
            foreach ($this->fixes as $fix) {
                if (!$fix['success']) {
                    echo "  • {$fix['category']}: {$fix['description']}\n";
                }
            }
            echo "\n";
        }
        
        // 保存修复报告
        $reportData = [
            'fix_time' => date('Y-m-d H:i:s'),
            'total_fixes' => $total,
            'successful_fixes' => $successCount,
            'failed_fixes' => $failedCount,
            'success_rate' => round(($successCount / $total) * 100, 1),
            'fixes' => $this->fixes
        ];
        
        file_put_contents('user_system_fix_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "📄 修复报告已保存到: user_system_fix_report.json\n";
        
        if ($successCount === $total) {
            echo "\n🎉 用户系统修复完成！\n";
        } elseif ($successCount / $total >= 0.8) {
            echo "\n⚠️  用户系统基本修复完成，有少量问题需要手动处理\n";
        } else {
            echo "\n❌ 用户系统存在较多问题，需要进一步修复\n";
        }
    }
}

// 主程序
$fixer = new UserSystemFixer();
$fixer->runAllFixes();

echo "\n=== 用户系统修复完成 ===\n";

?>
