<?php

echo "=== Boyou BBS 6.1 最终系统验证 ===\n\n";

class FinalSystemVerifier {
    private $results = [];
    private $baseUrl = 'http://localhost:8000';
    
    public function runCompleteVerification() {
        echo "开始最终系统验证...\n\n";
        
        $this->verifySystemStatus();
        $this->verifyDatabaseIntegrity();
        $this->verifyWebServerResponse();
        $this->verifyUserSystemBasics();
        $this->verifyAdminSystemBasics();
        $this->verifySecurityFeatures();
        $this->generateFinalReport();
    }
    
    private function verifySystemStatus() {
        echo "1. 验证系统状态...\n";
        
        // 检查关键文件
        $criticalFiles = [
            'index.php' => '主入口文件',
            'conf/conf.php' => '配置文件',
            'data/boyou_bbs.db' => '数据库文件',
            'boyouphp/boyouphp.php' => '框架文件'
        ];
        
        foreach ($criticalFiles as $file => $description) {
            if (file_exists($file)) {
                $this->addResult('系统文件', $description, true, '文件存在');
            } else {
                $this->addResult('系统文件', $description, false, '文件缺失');
            }
        }
        
        // 检查目录权限
        $directories = [
            'tmp' => '临时目录',
            'upload' => '上传目录',
            'log' => '日志目录'
        ];
        
        foreach ($directories as $dir => $description) {
            if (is_dir($dir) && is_writable($dir)) {
                $this->addResult('目录权限', $description, true, '可写');
            } else {
                $this->addResult('目录权限', $description, false, '不可写或不存在');
            }
        }
    }
    
    private function verifyDatabaseIntegrity() {
        echo "2. 验证数据库完整性...\n";
        
        try {
            $pdo = new PDO('sqlite:data/boyou_bbs.db');
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $this->addResult('数据库连接', '数据库连接', true, '连接成功');
            
            // 检查核心表
            $requiredTables = [
                'bbs_user' => '用户表',
                'bbs_forum' => '版块表',
                'bbs_thread' => '主题表',
                'bbs_post' => '帖子表'
            ];
            
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
            $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($requiredTables as $table => $description) {
                if (in_array($table, $existingTables)) {
                    // 检查表中是否有数据
                    $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                    $count = $stmt->fetchColumn();
                    $this->addResult('数据库表', $description, true, "存在，包含 $count 条记录");
                } else {
                    $this->addResult('数据库表', $description, false, '表不存在');
                }
            }
            
        } catch (PDOException $e) {
            $this->addResult('数据库连接', '数据库连接', false, '连接失败: ' . $e->getMessage());
        }
    }
    
    private function verifyWebServerResponse() {
        echo "3. 验证Web服务器响应...\n";
        
        // 检查服务器是否运行
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'method' => 'GET'
            ]
        ]);
        
        $response = @file_get_contents($this->baseUrl, false, $context);
        
        if ($response !== false) {
            $this->addResult('Web服务器', '服务器响应', true, '服务器正常响应');
            
            // 检查响应内容
            if (strpos($response, 'Boyou BBS') !== false) {
                $this->addResult('页面内容', '品牌显示', true, '页面包含Boyou BBS品牌');
            } else {
                $this->addResult('页面内容', '品牌显示', false, '页面不包含品牌信息');
            }
            
            if (strpos($response, '<html') !== false && strpos($response, '</html>') !== false) {
                $this->addResult('页面结构', 'HTML结构', true, 'HTML结构完整');
            } else {
                $this->addResult('页面结构', 'HTML结构', false, 'HTML结构不完整');
            }
            
        } else {
            $this->addResult('Web服务器', '服务器响应', false, '服务器无响应');
        }
    }
    
    private function verifyUserSystemBasics() {
        echo "4. 验证用户系统基础功能...\n";
        
        // 测试登录页面
        $loginPage = @file_get_contents($this->baseUrl . '/user-login');
        if ($loginPage && strpos($loginPage, 'login') !== false) {
            $this->addResult('用户系统', '登录页面', true, '登录页面可访问');
        } else {
            $this->addResult('用户系统', '登录页面', false, '登录页面不可访问');
        }
        
        // 测试注册页面
        $registerPage = @file_get_contents($this->baseUrl . '/user-create');
        if ($registerPage && strpos($registerPage, 'create') !== false) {
            $this->addResult('用户系统', '注册页面', true, '注册页面可访问');
        } else {
            $this->addResult('用户系统', '注册页面', false, '注册页面不可访问');
        }
        
        // 测试用户页面
        $userPage = @file_get_contents($this->baseUrl . '/user');
        if ($userPage) {
            $this->addResult('用户系统', '用户页面', true, '用户页面可访问');
        } else {
            $this->addResult('用户系统', '用户页面', false, '用户页面不可访问');
        }
    }
    
    private function verifyAdminSystemBasics() {
        echo "5. 验证管理后台基础功能...\n";
        
        // 测试管理后台页面
        $adminPage = @file_get_contents($this->baseUrl . '/admin/');
        if ($adminPage) {
            $this->addResult('管理后台', '管理后台首页', true, '管理后台可访问');
            
            if (strpos($adminPage, 'admin') !== false || strpos($adminPage, '管理') !== false) {
                $this->addResult('管理后台', '管理后台内容', true, '包含管理后台特征');
            } else {
                $this->addResult('管理后台', '管理后台内容', false, '不包含管理后台特征');
            }
        } else {
            $this->addResult('管理后台', '管理后台首页', false, '管理后台不可访问');
        }
    }
    
    private function verifySecurityFeatures() {
        echo "6. 验证安全特性...\n";
        
        // 检查HTTPS重定向（如果配置了）
        $headers = @get_headers($this->baseUrl, 1);
        if ($headers) {
            $this->addResult('安全特性', 'HTTP响应', true, 'HTTP头部正常');
            
            // 检查安全头部
            $securityHeaders = [
                'X-Content-Type-Options' => 'MIME类型保护',
                'X-Frame-Options' => '点击劫持保护',
                'X-XSS-Protection' => 'XSS保护'
            ];
            
            foreach ($securityHeaders as $header => $description) {
                $found = false;
                foreach ($headers as $key => $value) {
                    if (stripos($key, $header) !== false) {
                        $found = true;
                        break;
                    }
                }
                
                if ($found) {
                    $this->addResult('安全头部', $description, true, '已设置');
                } else {
                    $this->addResult('安全头部', $description, false, '未设置');
                }
            }
        } else {
            $this->addResult('安全特性', 'HTTP响应', false, 'HTTP头部获取失败');
        }
        
        // 检查敏感文件访问
        $sensitiveFiles = [
            '/conf/conf.php' => '配置文件',
            '/data/boyou_bbs.db' => '数据库文件',
            '/.git/' => 'Git目录'
        ];
        
        foreach ($sensitiveFiles as $file => $description) {
            $response = @file_get_contents($this->baseUrl . $file);
            if ($response === false || empty($response)) {
                $this->addResult('文件保护', $description, true, '文件受保护');
            } else {
                $this->addResult('文件保护', $description, false, '文件可直接访问');
            }
        }
    }
    
    private function addResult($category, $item, $success, $message) {
        $this->results[] = [
            'category' => $category,
            'item' => $item,
            'success' => $success,
            'message' => $message
        ];
        
        $status = $success ? '✓' : '✗';
        echo "  $status $item: $message\n";
    }
    
    private function generateFinalReport() {
        echo "\n=== 最终系统验证报告 ===\n\n";
        
        // 统计结果
        $total = count($this->results);
        $successful = array_filter($this->results, function($result) {
            return $result['success'];
        });
        $successCount = count($successful);
        $failedCount = $total - $successCount;
        
        echo "📊 验证统计:\n";
        echo "  总验证项: $total\n";
        echo "  通过验证: $successCount\n";
        echo "  失败验证: $failedCount\n";
        echo "  成功率: " . round(($successCount / $total) * 100, 1) . "%\n\n";
        
        // 按类别分组显示结果
        $categories = [];
        foreach ($this->results as $result) {
            $categories[$result['category']][] = $result;
        }
        
        echo "📋 分类结果:\n";
        foreach ($categories as $category => $items) {
            $categorySuccess = array_filter($items, function($item) { return $item['success']; });
            $categorySuccessRate = round((count($categorySuccess) / count($items)) * 100, 1);
            
            echo "  $category: " . count($categorySuccess) . "/" . count($items) . " ($categorySuccessRate%)\n";
        }
        
        echo "\n";
        
        // 显示失败的项目
        if ($failedCount > 0) {
            echo "❌ 需要关注的问题:\n";
            foreach ($this->results as $result) {
                if (!$result['success']) {
                    echo "  • {$result['category']} - {$result['item']}: {$result['message']}\n";
                }
            }
            echo "\n";
        }
        
        // 生成系统状态评估
        $this->generateSystemAssessment($successCount, $total);
        
        // 保存详细报告
        $reportData = [
            'verification_time' => date('Y-m-d H:i:s'),
            'total_checks' => $total,
            'successful_checks' => $successCount,
            'failed_checks' => $failedCount,
            'success_rate' => round(($successCount / $total) * 100, 1),
            'results' => $this->results,
            'categories' => $categories
        ];
        
        file_put_contents('final_verification_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "📄 详细验证报告已保存到: final_verification_report.json\n";
    }
    
    private function generateSystemAssessment($successCount, $total) {
        $successRate = ($successCount / $total) * 100;
        
        echo "🎯 系统状态评估:\n";
        
        if ($successRate >= 90) {
            echo "  🟢 优秀 - 系统运行良好，可以投入使用\n";
            echo "  建议: 定期维护和监控\n";
        } elseif ($successRate >= 75) {
            echo "  🟡 良好 - 系统基本可用，有少量问题需要处理\n";
            echo "  建议: 修复失败的验证项，然后投入使用\n";
        } elseif ($successRate >= 60) {
            echo "  🟠 一般 - 系统存在一些问题，需要进一步完善\n";
            echo "  建议: 优先修复关键问题，完善后再投入使用\n";
        } else {
            echo "  🔴 需要改进 - 系统存在较多问题，不建议立即投入使用\n";
            echo "  建议: 全面检查和修复问题，重新验证后再考虑使用\n";
        }
        
        echo "\n💡 下一步建议:\n";
        echo "  1. 查看详细验证报告，了解具体问题\n";
        echo "  2. 优先修复安全相关的问题\n";
        echo "  3. 完善用户系统和管理后台功能\n";
        echo "  4. 进行充分的功能测试\n";
        echo "  5. 配置生产环境的安全设置\n";
    }
}

// 主程序
$verifier = new FinalSystemVerifier();
$verifier->runCompleteVerification();

echo "\n=== 最终系统验证完成 ===\n";

?>
