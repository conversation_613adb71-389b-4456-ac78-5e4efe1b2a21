<?php

echo "=== Boyou BBS 6.1 综合测试报告 ===\n\n";

class ComprehensiveTestReporter {
    private $reports = [];
    
    public function generateReport() {
        echo "正在生成综合测试报告...\n\n";
        
        $this->loadTestReports();
        $this->analyzeResults();
        $this->generateSummary();
        $this->generateRecommendations();
        $this->saveComprehensiveReport();
    }
    
    private function loadTestReports() {
        $reportFiles = [
            'website_test_report.json' => '网站功能测试',
            'page_display_report.json' => '页面显示测试',
            'user_functionality_report.json' => '用户功能测试',
            'admin_functionality_report.json' => '管理后台功能测试'
        ];
        
        foreach ($reportFiles as $file => $description) {
            if (file_exists($file)) {
                $data = json_decode(file_get_contents($file), true);
                if ($data) {
                    $this->reports[$description] = $data;
                    echo "✓ 已加载: $description\n";
                } else {
                    echo "✗ 加载失败: $description (JSON格式错误)\n";
                }
            } else {
                echo "⚠️  文件不存在: $description\n";
            }
        }
        echo "\n";
    }
    
    private function analyzeResults() {
        echo "=== 测试结果分析 ===\n\n";
        
        $totalTests = 0;
        $totalPassed = 0;
        $totalFailed = 0;
        
        foreach ($this->reports as $testType => $report) {
            $tests = $report['total_tests'] ?? count($report['results'] ?? []);
            $passed = $report['passed_tests'] ?? 0;
            $failed = $report['failed_tests'] ?? 0;
            $successRate = $report['success_rate'] ?? 0;
            
            echo "📊 $testType:\n";
            echo "   总测试数: $tests\n";
            echo "   通过测试: $passed\n";
            echo "   失败测试: $failed\n";
            echo "   成功率: $successRate%\n";
            
            // 评估等级
            if ($successRate >= 95) {
                echo "   评估: 🟢 优秀\n";
            } elseif ($successRate >= 80) {
                echo "   评估: 🟡 良好\n";
            } elseif ($successRate >= 60) {
                echo "   评估: 🟠 一般\n";
            } else {
                echo "   评估: 🔴 需要改进\n";
            }
            echo "\n";
            
            $totalTests += $tests;
            $totalPassed += $passed;
            $totalFailed += $failed;
        }
        
        echo "📈 总体统计:\n";
        echo "   总测试数: $totalTests\n";
        echo "   总通过数: $totalPassed\n";
        echo "   总失败数: $totalFailed\n";
        echo "   总成功率: " . round(($totalPassed / $totalTests) * 100, 1) . "%\n\n";
    }
    
    private function generateSummary() {
        echo "=== 测试总结 ===\n\n";
        
        // 分析各个模块的状态
        $moduleStatus = [];
        
        foreach ($this->reports as $testType => $report) {
            $successRate = $report['success_rate'] ?? 0;
            
            if ($successRate >= 95) {
                $moduleStatus[$testType] = '优秀';
            } elseif ($successRate >= 80) {
                $moduleStatus[$testType] = '良好';
            } elseif ($successRate >= 60) {
                $moduleStatus[$testType] = '一般';
            } else {
                $moduleStatus[$testType] = '需要改进';
            }
        }
        
        echo "🎯 各模块状态:\n";
        foreach ($moduleStatus as $module => $status) {
            $icon = $this->getStatusIcon($status);
            echo "   $icon $module: $status\n";
        }
        echo "\n";
        
        // 主要发现
        echo "🔍 主要发现:\n";
        
        // 网站功能
        if (isset($this->reports['网站功能测试'])) {
            $websiteReport = $this->reports['网站功能测试'];
            if ($websiteReport['success_rate'] >= 90) {
                echo "   ✅ 网站基础功能运行良好，框架加载正常\n";
            }
        }
        
        // 页面显示
        if (isset($this->reports['页面显示测试'])) {
            $pageReport = $this->reports['页面显示测试'];
            if ($pageReport['success_rate'] == 100) {
                echo "   ✅ 所有页面都能正常显示，响应式设计工作正常\n";
            }
        }
        
        // 用户功能
        if (isset($this->reports['用户功能测试'])) {
            $userReport = $this->reports['用户功能测试'];
            if ($userReport['success_rate'] < 50) {
                echo "   ⚠️  用户功能存在较多问题，需要重点关注\n";
            }
        }
        
        // 管理后台
        if (isset($this->reports['管理后台功能测试'])) {
            $adminReport = $this->reports['管理后台功能测试'];
            if ($adminReport['success_rate'] >= 60) {
                echo "   ✅ 管理后台基本可用，部分功能需要完善\n";
            }
        }
        
        echo "\n";
    }
    
    private function generateRecommendations() {
        echo "=== 改进建议 ===\n\n";
        
        $recommendations = [];
        
        // 基于测试结果生成建议
        foreach ($this->reports as $testType => $report) {
            $successRate = $report['success_rate'] ?? 0;
            $failedTests = [];
            
            if (isset($report['results'])) {
                foreach ($report['results'] as $result) {
                    if (!$result['success']) {
                        $failedTests[] = $result['test'];
                    }
                }
            }
            
            if ($successRate < 80 && !empty($failedTests)) {
                $recommendations[] = [
                    'module' => $testType,
                    'priority' => $successRate < 50 ? '高' : '中',
                    'issues' => $failedTests
                ];
            }
        }
        
        // 按优先级排序
        usort($recommendations, function($a, $b) {
            $priorityOrder = ['高' => 3, '中' => 2, '低' => 1];
            return $priorityOrder[$b['priority']] - $priorityOrder[$a['priority']];
        });
        
        $priorityCount = 1;
        foreach ($recommendations as $rec) {
            $icon = $rec['priority'] === '高' ? '🔴' : '🟡';
            echo "$icon 优先级{$rec['priority']} - {$rec['module']}:\n";
            
            foreach (array_slice($rec['issues'], 0, 5) as $issue) {
                echo "   • $issue\n";
            }
            
            if (count($rec['issues']) > 5) {
                $remaining = count($rec['issues']) - 5;
                echo "   • ... 还有 $remaining 个问题\n";
            }
            echo "\n";
            
            $priorityCount++;
        }
        
        // 通用建议
        echo "💡 通用建议:\n";
        echo "   1. 优先修复用户注册和登录功能\n";
        echo "   2. 完善管理后台的权限控制\n";
        echo "   3. 添加CSRF保护机制\n";
        echo "   4. 完善错误处理和用户反馈\n";
        echo "   5. 定期进行功能测试和安全检查\n\n";
    }
    
    private function saveComprehensiveReport() {
        $reportData = [
            'generated_at' => date('Y-m-d H:i:s'),
            'test_summary' => $this->calculateSummary(),
            'individual_reports' => $this->reports,
            'overall_assessment' => $this->getOverallAssessment()
        ];
        
        // 保存JSON格式报告
        file_put_contents('comprehensive_test_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        // 保存Markdown格式报告
        $this->saveMarkdownReport($reportData);
        
        echo "📄 综合测试报告已保存:\n";
        echo "   • comprehensive_test_report.json (JSON格式)\n";
        echo "   • comprehensive_test_report.md (Markdown格式)\n\n";
    }
    
    private function calculateSummary() {
        $totalTests = 0;
        $totalPassed = 0;
        
        foreach ($this->reports as $report) {
            $totalTests += $report['total_tests'] ?? count($report['results'] ?? []);
            $totalPassed += $report['passed_tests'] ?? 0;
        }
        
        return [
            'total_tests' => $totalTests,
            'total_passed' => $totalPassed,
            'total_failed' => $totalTests - $totalPassed,
            'overall_success_rate' => $totalTests > 0 ? round(($totalPassed / $totalTests) * 100, 1) : 0
        ];
    }
    
    private function getOverallAssessment() {
        $summary = $this->calculateSummary();
        $successRate = $summary['overall_success_rate'];
        
        if ($successRate >= 90) {
            return '系统整体运行良好，可以投入使用';
        } elseif ($successRate >= 75) {
            return '系统基本可用，建议修复主要问题后投入使用';
        } elseif ($successRate >= 60) {
            return '系统存在一些问题，需要进一步完善';
        } else {
            return '系统存在较多问题，建议全面检查和修复';
        }
    }
    
    private function saveMarkdownReport($data) {
        $md = "# Boyou BBS 6.1 综合测试报告\n\n";
        $md .= "**生成时间:** " . $data['generated_at'] . "\n\n";
        
        $md .= "## 测试概览\n\n";
        $summary = $data['test_summary'];
        $md .= "- **总测试数:** {$summary['total_tests']}\n";
        $md .= "- **通过测试:** {$summary['total_passed']}\n";
        $md .= "- **失败测试:** {$summary['total_failed']}\n";
        $md .= "- **总成功率:** {$summary['overall_success_rate']}%\n\n";
        
        $md .= "## 整体评估\n\n";
        $md .= $data['overall_assessment'] . "\n\n";
        
        $md .= "## 各模块测试结果\n\n";
        foreach ($data['individual_reports'] as $testType => $report) {
            $md .= "### $testType\n\n";
            $md .= "- 成功率: {$report['success_rate']}%\n";
            $md .= "- 通过: {$report['passed_tests']}/{$report['total_tests']}\n\n";
        }
        
        file_put_contents('comprehensive_test_report.md', $md);
    }
    
    private function getStatusIcon($status) {
        switch ($status) {
            case '优秀': return '🟢';
            case '良好': return '🟡';
            case '一般': return '🟠';
            case '需要改进': return '🔴';
            default: return '⚪';
        }
    }
}

// 主程序
$reporter = new ComprehensiveTestReporter();
$reporter->generateReport();

echo "=== 综合测试报告生成完成 ===\n";

?>
