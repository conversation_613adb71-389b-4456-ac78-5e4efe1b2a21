<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Debug Information ===\n";

// 检查基本路径
define('APP_PATH', __DIR__.'/');
define('ADMIN_PATH', APP_PATH.'admin/');
define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');

echo "APP_PATH: " . APP_PATH . "\n";
echo "BOYOUPHP_PATH: " . BOYOUPHP_PATH . "\n";

// 检查安装锁文件
$install_lock = APP_PATH.'data/install.lock';
echo "Install lock exists: " . (file_exists($install_lock) ? 'Yes' : 'No') . "\n";

// 检查配置文件
$conf_file = APP_PATH.'conf/conf.php';
echo "Config file exists: " . (file_exists($conf_file) ? 'Yes' : 'No') . "\n";

if (file_exists($conf_file)) {
    echo "Loading config...\n";
    $conf = include $conf_file;
    if ($conf) {
        echo "Config loaded successfully\n";
        echo "Site name: " . $conf['sitename'] . "\n";
        echo "Database path: " . $conf['db']['pdo_sqlite']['master']['path'] . "\n";
    } else {
        echo "Failed to load config\n";
    }
}

// 检查 boyouphp 文件
$boyouphp_file = BOYOUPHP_PATH.'boyouphp.min.php';
echo "BoyouPHP file exists: " . (file_exists($boyouphp_file) ? 'Yes' : 'No') . "\n";

if (file_exists($boyouphp_file)) {
    echo "Including BoyouPHP...\n";
    try {
        include $boyouphp_file;
        echo "BoyouPHP included successfully\n";
    } catch (Exception $e) {
        echo "Error including BoyouPHP: " . $e->getMessage() . "\n";
    }
}

// 检查模型文件
$model_file = APP_PATH.'model.inc.php';
echo "Model file exists: " . (file_exists($model_file) ? 'Yes' : 'No') . "\n";

$index_inc_file = APP_PATH.'index.inc.php';
echo "Index inc file exists: " . (file_exists($index_inc_file) ? 'Yes' : 'No') . "\n";

echo "=== End Debug ===\n";
?>
