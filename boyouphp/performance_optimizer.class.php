<?php

/**
 * 性能优化器类
 * 提供缓存管理、内存优化、查询优化等功能
 */
class performance_optimizer {
    
    private static ?self $instance = null;
    private array $cache = [];
    private array $queryStats = [];
    private float $startTime;
    private int $memoryStart;
    
    private function __construct() {
        $this->startTime = microtime(true);
        $this->memoryStart = memory_get_usage();
    }
    
    /**
     * 获取单例实例
     * @return self
     */
    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 设置缓存
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $ttl 过期时间（秒）
     * @return bool
     */
    public function setCache(string $key, $value, int $ttl = 3600): bool {
        $this->cache[$key] = [
            'value' => $value,
            'expire' => time() + $ttl,
            'hits' => 0
        ];
        return true;
    }
    
    /**
     * 获取缓存
     * @param string $key 缓存键
     * @return mixed|null
     */
    public function getCache(string $key) {
        if (!isset($this->cache[$key])) {
            return null;
        }
        
        $item = $this->cache[$key];
        
        // 检查是否过期
        if ($item['expire'] < time()) {
            unset($this->cache[$key]);
            return null;
        }
        
        // 增加命中次数
        $this->cache[$key]['hits']++;
        
        return $item['value'];
    }
    
    /**
     * 删除缓存
     * @param string $key 缓存键
     * @return bool
     */
    public function deleteCache(string $key): bool {
        unset($this->cache[$key]);
        return true;
    }
    
    /**
     * 清空所有缓存
     * @return bool
     */
    public function clearCache(): bool {
        $this->cache = [];
        return true;
    }
    
    /**
     * 记录查询统计
     * @param string $sql SQL语句
     * @param float $time 执行时间
     * @return void
     */
    public function recordQuery(string $sql, float $time): void {
        $sqlHash = md5($sql);
        
        if (!isset($this->queryStats[$sqlHash])) {
            $this->queryStats[$sqlHash] = [
                'sql' => $sql,
                'count' => 0,
                'total_time' => 0,
                'avg_time' => 0,
                'max_time' => 0
            ];
        }
        
        $this->queryStats[$sqlHash]['count']++;
        $this->queryStats[$sqlHash]['total_time'] += $time;
        $this->queryStats[$sqlHash]['avg_time'] = $this->queryStats[$sqlHash]['total_time'] / $this->queryStats[$sqlHash]['count'];
        $this->queryStats[$sqlHash]['max_time'] = max($this->queryStats[$sqlHash]['max_time'], $time);
    }
    
    /**
     * 获取性能统计信息
     * @return array
     */
    public function getStats(): array {
        $currentTime = microtime(true);
        $currentMemory = memory_get_usage();
        
        return [
            'execution_time' => round($currentTime - $this->startTime, 4),
            'memory_usage' => $currentMemory - $this->memoryStart,
            'memory_peak' => memory_get_peak_usage(),
            'cache_items' => count($this->cache),
            'cache_hits' => array_sum(array_column($this->cache, 'hits')),
            'query_count' => array_sum(array_column($this->queryStats, 'count')),
            'slow_queries' => $this->getSlowQueries()
        ];
    }
    
    /**
     * 获取慢查询列表
     * @param float $threshold 慢查询阈值（秒）
     * @return array
     */
    public function getSlowQueries(float $threshold = 0.1): array {
        $slowQueries = [];
        
        foreach ($this->queryStats as $stat) {
            if ($stat['max_time'] > $threshold) {
                $slowQueries[] = $stat;
            }
        }
        
        // 按最大执行时间排序
        usort($slowQueries, function($a, $b) {
            return $b['max_time'] <=> $a['max_time'];
        });
        
        return $slowQueries;
    }
    
    /**
     * 优化内存使用
     * @return void
     */
    public function optimizeMemory(): void {
        // 清理过期缓存
        $now = time();
        foreach ($this->cache as $key => $item) {
            if ($item['expire'] < $now) {
                unset($this->cache[$key]);
            }
        }
        
        // 强制垃圾回收
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }
    
    /**
     * 获取缓存统计信息
     * @return array
     */
    public function getCacheStats(): array {
        $totalHits = 0;
        $expiredCount = 0;
        $now = time();
        
        foreach ($this->cache as $item) {
            $totalHits += $item['hits'];
            if ($item['expire'] < $now) {
                $expiredCount++;
            }
        }
        
        return [
            'total_items' => count($this->cache),
            'total_hits' => $totalHits,
            'expired_items' => $expiredCount,
            'hit_rate' => count($this->cache) > 0 ? round($totalHits / count($this->cache), 2) : 0
        ];
    }
    
    /**
     * 输出性能报告
     * @param bool $detailed 是否显示详细信息
     * @return string
     */
    public function getPerformanceReport(bool $detailed = false): string {
        $stats = $this->getStats();
        $cacheStats = $this->getCacheStats();
        
        $report = "=== 性能报告 ===\n";
        $report .= "执行时间: {$stats['execution_time']}s\n";
        $report .= "内存使用: " . round($stats['memory_usage'] / 1024 / 1024, 2) . "MB\n";
        $report .= "内存峰值: " . round($stats['memory_peak'] / 1024 / 1024, 2) . "MB\n";
        $report .= "查询次数: {$stats['query_count']}\n";
        $report .= "缓存项目: {$cacheStats['total_items']}\n";
        $report .= "缓存命中: {$cacheStats['total_hits']}\n";
        $report .= "慢查询数: " . count($stats['slow_queries']) . "\n";
        
        if ($detailed && !empty($stats['slow_queries'])) {
            $report .= "\n=== 慢查询详情 ===\n";
            foreach (array_slice($stats['slow_queries'], 0, 5) as $query) {
                $report .= "时间: {$query['max_time']}s, 次数: {$query['count']}, SQL: " . substr($query['sql'], 0, 100) . "...\n";
            }
        }
        
        return $report;
    }
}
