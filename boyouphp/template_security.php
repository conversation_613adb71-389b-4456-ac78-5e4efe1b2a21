<?php
// 模板安全函数

// 安全输出函数
function safe_echo($value, $escape = true) {
    if ($escape) {
        echo htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    } else {
        echo $value;
    }
}

// 安全属性输出
function safe_attr($value) {
    return htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
}

// 安全URL输出
function safe_url($url) {
    return htmlspecialchars(filter_var($url, FILTER_SANITIZE_URL), ENT_QUOTES | ENT_HTML5, 'UTF-8');
}

// 安全JavaScript输出
function safe_js($value) {
    return json_encode($value, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
}

// 安全CSS输出
function safe_css($value) {
    // 移除潜在危险的CSS
    $value = preg_replace('/[^a-zA-Z0-9\s\-_#.,;:()%]/', '', $value);
    return htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
}

// 富文本安全输出（允许部分HTML标签）
function safe_html($html, $allowedTags = '<p><br><strong><em><u><a><img><ul><ol><li>') {
    // 使用strip_tags保留允许的标签
    $html = strip_tags($html, $allowedTags);
    
    // 进一步过滤属性
    $html = preg_replace('/(<a[^>]*href=")([^"]*)/i', '$1' . safe_url('$2'), $html);
    $html = preg_replace('/(<img[^>]*src=")([^"]*)/i', '$1' . safe_url('$2'), $html);
    
    return $html;
}

// 模板变量过滤器
function template_filter($value, $filter = 'html') {
    switch ($filter) {
        case 'html':
            return htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        case 'attr':
            return safe_attr($value);
        case 'url':
            return safe_url($value);
        case 'js':
            return safe_js($value);
        case 'css':
            return safe_css($value);
        case 'rich':
            return safe_html($value);
        case 'raw':
            return $value; // 不过滤，谨慎使用
        default:
            return htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
}
?>