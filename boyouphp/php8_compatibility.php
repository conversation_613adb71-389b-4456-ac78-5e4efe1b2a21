<?php
// PHP 8.x 兼容性函数

if (!function_exists('is_countable')) {
    function is_countable($value) {
        return is_array($value) || $value instanceof Countable;
    }
}

// 安全的 count 函数
if (!function_exists('safe_count')) {
    function safe_count($value) {
        return is_countable($value) ? count($value) : 0;
    }
}

// 安全的 strlen 函数
if (!function_exists('safe_strlen')) {
    function safe_strlen($value) {
        return is_string($value) ? strlen($value) : 0;
    }
}

// 安全的数组访问函数
if (!function_exists('safe_array_get')) {
    function safe_array_get($array, $key, $default = null) {
        return isset($array[$key]) ? $array[$key] : $default;
    }
}

// 安全的对象属性访问函数
if (!function_exists('safe_object_get')) {
    function safe_object_get($object, $property, $default = null) {
        return isset($object->$property) ? $object->$property : $default;
    }
}
?>