<?php
// 性能监控脚本
class PerformanceMonitor {
    private $startTime;
    private $startMemory;
    private $queries = [];
    
    public function __construct() {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage();
    }
    
    public function addQuery($sql, $time) {
        $this->queries[] = [
            'sql' => $sql,
            'time' => $time,
            'memory' => memory_get_usage()
        ];
    }
    
    public function getReport() {
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        return [
            'execution_time' => round(($endTime - $this->startTime) * 1000, 2),
            'memory_usage' => round(($endMemory - $this->startMemory) / 1024, 2),
            'peak_memory' => round(memory_get_peak_usage() / 1024, 2),
            'query_count' => count($this->queries),
            'slow_queries' => array_filter($this->queries, function($q) {
                return $q['time'] > 0.1; // 慢查询阈值100ms
            })
        ];
    }
    
    public function logReport() {
        $report = $this->getReport();
        $logEntry = date('Y-m-d H:i:s') . ' - ' . json_encode($report) . "\n";
        file_put_contents('./log/performance.log', $logEntry, FILE_APPEND | LOCK_EX);
    }
}

// 全局性能监控实例
if (!isset($GLOBALS['performance_monitor'])) {
    $GLOBALS['performance_monitor'] = new PerformanceMonitor();
}
?>