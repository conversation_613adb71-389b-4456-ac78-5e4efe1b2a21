<?php

/**
 * MySQL数据库操作类 - 使用MySQLi扩展
 * 支持主从分离、连接池、事务处理
 */
class db_mysql {

	public array $conf = []; // 配置，可以支持主从
	public array $rconf = []; // 读库配置
	public ?\mysqli $wlink = null;  // 写连接 (mysqli resource)
	public ?\mysqli $rlink = null;  // 读连接 (mysqli resource)
	public ?\mysqli $link = null;   // 最后一次使用的连接
	public int $errno = 0;
	public string $errstr = '';
	public array $sqls = [];
	public string $tablepre = '';
	public bool $innodb_first = true;// 优先 InnoDB
	
	/**
	 * 构造函数
	 * @param array $conf 数据库配置
	 */
	public function __construct(array $conf) {
		$this->conf = $conf;
		$this->tablepre = $conf['master']['tablepre'] ?? '';
	}
	
	/**
	 * 根据配置文件连接数据库
	 * @return bool 连接是否成功
	 */
	public function connect(): bool {
		$this->wlink = $this->connect_master();
		$this->rlink = $this->connect_slave();
		return $this->wlink !== null && $this->rlink !== null;
	}
	
	/**
	 * 连接主数据库（写服务器）
	 * @return ?\mysqli 数据库连接资源
	 */
	public function connect_master(): ?\mysqli {
		if ($this->wlink) {
			return $this->wlink;
		}

		$conf = $this->conf['master'] ?? [];
		if (empty($conf)) {
			$this->error(0, '主数据库配置为空');
			return null;
		}

		$this->wlink = $this->real_connect(
			$conf['host'] ?? 'localhost',
			$conf['user'] ?? '',
			$conf['password'] ?? '',
			$conf['name'] ?? '',
			$conf['charset'] ?? 'utf8',
			$conf['engine'] ?? 'innodb'
		);

		return $this->wlink;
	}
	
	/**
	 * 连接从数据库（读服务器）
	 * 如果有多台从服务器，则随机挑选一台；如果没有配置从服务器，则使用主服务器
	 * @return ?\mysqli 数据库连接资源
	 */
	public function connect_slave(): ?\mysqli {
		if ($this->rlink) {
			return $this->rlink;
		}

		if (empty($this->conf['slaves'])) {
			// 没有从服务器配置，使用主服务器
			if ($this->wlink === null) {
				$this->wlink = $this->connect_master();
			}
			$this->rlink = $this->wlink;
			$this->rconf = $this->conf['master'] ?? [];
		} else {
			// 随机选择一台从服务器
			$slaves = $this->conf['slaves'];
			$n = array_rand($slaves);
			$conf = $slaves[$n];
			$this->rconf = $conf;

			$this->rlink = $this->real_connect(
				$conf['host'] ?? 'localhost',
				$conf['user'] ?? '',
				$conf['password'] ?? '',
				$conf['name'] ?? '',
				$conf['charset'] ?? 'utf8',
				$conf['engine'] ?? 'innodb'
			);
		}

		return $this->rlink;
	}
	
	/**
	 * 建立数据库连接
	 * @param string $host 主机地址
	 * @param string $user 用户名
	 * @param string $password 密码
	 * @param string $name 数据库名
	 * @param string $charset 字符集
	 * @param string $engine 存储引擎
	 * @return ?\mysqli 数据库连接资源
	 */
	public function real_connect(string $host, string $user, string $password, string $name, string $charset = 'utf8', string $engine = 'innodb'): ?\mysqli {
		// 使用 mysqli_connect 替代已弃用的 mysql_connect
		$link = @mysqli_connect($host, $user, $password, $name);

		if (!$link) {
			$this->error(mysqli_connect_errno(), '连接数据库服务器失败: ' . mysqli_connect_error());
			return null;
		}

		// 设置字符集和SQL模式
		if ($charset) {
			if (!mysqli_set_charset($link, $charset)) {
				$this->error(mysqli_errno($link), '设置字符集失败: ' . mysqli_error($link));
				mysqli_close($link);
				return null;
			}

			// 设置SQL模式为空，兼容旧版本
			$this->query("SET sql_mode=''", $link);
		}

		// InnoDB优化设置（可选）
		if (strtolower($engine) === 'innodb') {
			// $this->query("SET innodb_flush_log_at_trx_commit=0", $link);
		}

		return $link;
	}
	/**
	 * 执行SQL查询并返回单条记录
	 * @param string $sql SQL语句
	 * @return array|null 查询结果数组或null
	 */
	public function sql_find_one(string $sql): ?array {
		$query = $this->query($sql);
		if (!$query) {
			return null;
		}

		$result = mysqli_fetch_assoc($query);

		// 释放结果集
		if ($query instanceof \mysqli_result) {
			mysqli_free_result($query);
		}

		return $result ?: null;
	}


	/**
	 * 执行SQL查询并返回多条记录
	 * @param string $sql SQL语句
	 * @param string|null $key 用作数组键的字段名
	 * @return array 查询结果数组
	 */
	public function sql_find(string $sql, ?string $key = null): array {
		$query = $this->query($sql);
		if (!$query) {
			return [];
		}

		$arrlist = [];
		while ($arr = mysqli_fetch_assoc($query)) {
			if ($key && isset($arr[$key])) {
				$arrlist[$arr[$key]] = $arr;
			} else {
				$arrlist[] = $arr;
			}
		}

		// 释放结果集
		if ($query instanceof \mysqli_result) {
			mysqli_free_result($query);
		}

		return $arrlist;
	}
	
	public function find($table, $cond = [], $orderby = [], $page = 1, $pagesize = 10, $key = '', $col = []) {
		$page = max(1, $page);
		$cond = db_cond_to_sqladd($cond);
		$orderby = db_orderby_to_sqladd($orderby);
		$offset = ($page - 1) * $pagesize;
		$cols = $col ? implode(',', $col) : '*';
		return $this->sql_find("SELECT $cols FROM {$this->tablepre}$table $cond$orderby LIMIT $offset,$pagesize", $key);
		
	}
		
	public function find_one($table, $cond = [], $orderby = [], $col = []) {
		$cond = db_cond_to_sqladd($cond);
		$orderby = db_orderby_to_sqladd($orderby);
		$cols = $col ? implode(',', $col) : '*';
		return $this->sql_find_one("SELECT $cols FROM {$this->tablepre}$table $cond$orderby LIMIT 1");
	}
	
	public function query($sql, $link = NULL) {
		if(!$link) {
			if(!$this->rlink && !$this->connect_slave()) return FALSE;;
			$link = $this->link = $this->rlink;
		}
		$t1 = microtime(1);
		$query = mysqli_query($link, $sql);
		$t2 = microtime(1);
		if($query === FALSE) $this->error();

		$t3 = substr($t2 - $t1, 0, 6);
		DEBUG AND xn_log("[$t3]".$sql, 'db_sql');
		if(count($this->sqls) < 1000) $this->sqls[] = "[$t3]".$sql;

		return $query;
	}
	
	public function exec($sql, $link = NULL) {
		if(!$link) {
			if(!$this->wlink && !$this->connect_master()) return FALSE;
			$link = $this->link = $this->wlink;
		}
		if(strtoupper(substr($sql, 0, 12) == 'CREATE TABLE')) {
			$fulltext = strpos($sql, 'FULLTEXT(') !== FALSE;
			$highversion = version_compare($this->version(), '5.6') >= 0;
			if(!$fulltext || ($fulltext && $highversion)) {
				$conf = $this->conf['master'];
				if(strtolower($conf['engine']) != 'myisam') {
					$this->innodb_first AND $this->is_support_innodb() AND $sql = str_ireplace('MyISAM', 'InnoDB', $sql);
				}
			}
		}
		$t1 = microtime(1);
		$query = mysqli_query($this->wlink, $sql);
		$t2 = microtime(1);
		$t3 = substr($t2 - $t1, 0, 6);

		DEBUG AND xn_log("[$t3]".$sql, 'db_sql');
		if(count($this->sqls) < 1000) $this->sqls[] = "[$t3]".$sql;

		if($query !== FALSE) {
			$pre = strtoupper(substr(trim($sql), 0, 7));
			if($pre == 'INSERT ' || $pre == 'REPLACE') {
				return mysqli_insert_id($this->wlink);
			} elseif($pre == 'UPDATE ' || $pre == 'DELETE ') {
				return mysqli_affected_rows($this->wlink);
			}
		} else {
			$this->error();
		}
		
		return $query;
	}
	
	// 如果为 innodb，条件为空，并且有权限读取 information_schema
	public function count($table, $cond = []) {
		$this->connect_slave();
		if(empty($cond) && $this->rconf['engine'] == 'innodb') {
			$dbname = $this->rconf['name'];
			$sql = "SELECT TABLE_ROWS as num FROM information_schema.tables WHERE TABLE_SCHEMA='$dbname' AND TABLE_NAME='$table'";
		} else {
			$cond = db_cond_to_sqladd($cond);
			$sql = "SELECT COUNT(*) AS num FROM `$table` $cond";
		}
		$arr = $this->sql_find_one($sql);
		return !empty($arr) ? intval($arr['num']) : $arr;
	}
	
	public function maxid($table, $field, $cond = []) {
		$sqladd = db_cond_to_sqladd($cond);
		$sql = "SELECT MAX($field) AS maxid FROM `$table` $sqladd";
		$arr = $this->sql_find_one($sql);
		return !empty($arr) ? intval($arr['maxid']) : $arr;
	}
	
	public function truncate($table) {
		return $this->exec("TRUNCATE $table");
	}
	
	public function close() {
		$r = TRUE;
		if($this->wlink) {
			$r = mysqli_close($this->wlink);
		}
		if($this->rlink && $this->wlink != $this->rlink) {
			$r = mysqli_close($this->rlink) && $r;
		}
		return $r;
	}
	
	public function version() {
		$r = $this->sql_find_one("SELECT VERSION() AS v");
		return $r['v'];
	}
	
	public function error($errno = 0, $errstr = '') {
		$this->errno = $errno ? $errno : ($this->link ? mysqli_errno($this->link) : mysqli_connect_errno());
		$this->errstr = $errstr ? $errstr : ($this->link ? mysqli_error($this->link) : mysqli_connect_error());
		DEBUG AND trigger_error('Database Error:'.$this->errstr);
	}
	
	public function is_support_innodb() {
		$arrlist = $this->sql_find('SHOW ENGINES');
		$arrlist2 = arrlist_key_values($arrlist, 'Engine', 'Support');
		return isset($arrlist2['InnoDB']) AND $arrlist2['InnoDB'] == 'YES';
	}

	// pconnect 不释放连接
	public function __destruct() {
		if($this->wlink) $this->wlink = NULL;
		if($this->rlink) $this->rlink = NULL;
	}
}

?>