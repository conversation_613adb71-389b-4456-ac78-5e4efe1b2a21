<?php
// 安全中间件
class SecurityMiddleware {
    private $config;
    
    public function __construct($config = []) {
        $this->config = array_merge([
            'csrf_protection' => true,
            'xss_protection' => true,
            'rate_limit_enabled' => true
        ], $config);
    }
    
    public function handle() {
        // 设置安全头
        $this->setSecurityHeaders();
        
        // CSRF保护
        if ($this->config['csrf_protection'] && $_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->checkCSRF();
        }
        
        // XSS保护
        if ($this->config['xss_protection']) {
            $this->filterInput();
        }
        
        // 速率限制
        if ($this->config['rate_limit_enabled']) {
            $this->checkRateLimit();
        }
    }
    
    private function setSecurityHeaders() {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
    }
    
    private function checkCSRF() {
        $token = $_POST['csrf_token'] ?? '';
        if (!csrf_token_verify($token)) {
            http_response_code(403);
            die('CSRF token validation failed');
        }
    }
    
    private function filterInput() {
        $_GET = $this->filterArray($_GET);
        $_POST = $this->filterArray($_POST);
        $_REQUEST = $this->filterArray($_REQUEST);
    }
    
    private function filterArray($array) {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->filterArray($value);
            } else {
                $array[$key] = xn_input_filter($value);
            }
        }
        return $array;
    }
    
    private function checkRateLimit() {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $key = 'rate_limit_' . md5($ip);
        
        if (function_exists('apcu_fetch')) {
            $requests = apcu_fetch($key) ?: 0;
            if ($requests > 100) { // 每分钟100次请求
                http_response_code(429);
                die('Rate limit exceeded');
            }
            apcu_store($key, $requests + 1, 60);
        }
    }
}
?>