#!/bin/bash
# Boyou BBS 邮件服务快速配置脚本

echo "=== Boyou BBS 邮件服务快速配置 ==="
echo

read -p "请输入SMTP服务器地址: " smtp_server
read -p "请输入SMTP端口 (默认587): " smtp_port
smtp_port=${smtp_port:-587}
read -p "请输入加密方式 (tls/ssl，默认tls): " smtp_ssl
smtp_ssl=${smtp_ssl:-tls}
read -p "请输入邮箱用户名: " smtp_username
read -s -p "请输入邮箱密码/授权码: " smtp_password
echo
read -p "请输入发件人邮箱: " mail_from
read -p "请输入发件人名称 (默认Boyou BBS): " mail_from_name
mail_from_name=${mail_from_name:-"Boyou BBS"}

# 生成配置文件
cat > conf/smtp.conf.php << EOF
<?php
return [
    "smtp_on" => true,
    "smtp_server" => "$smtp_server",
    "smtp_port" => $smtp_port,
    "smtp_ssl" => "$smtp_ssl",
    "smtp_username" => "$smtp_username",
    "smtp_password" => "$smtp_password",
    "mail_from" => "$mail_from",
    "mail_from_name" => "$mail_from_name",
    "mail_charset" => "UTF-8",
    "mail_html" => true
];
?>
EOF

echo "✓ 邮件配置已保存到 conf/smtp.conf.php"
echo "✓ 配置完成！请测试邮件发送功能"
