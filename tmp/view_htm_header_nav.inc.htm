
	<!-- 
		轻论坛导航：默认头部
		Bootstrap 4.0 推荐的 PC/Mobile 公共写法 
		优点：公用性强
		缺点：结构稍微有点复杂
	-->
	
	
	<header class="navbar navbar-expand-lg navbar-dark bg-dark" id="header">
		<div class="container">
			<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="<?php echo lang('toggler_menu');?>">
				<span class="navbar-toggler-icon"></span>
			</button>
			
			
			
			<a class="navbar-brand text-truncate" href="<?php echo $header['mobile_link'];?>">
				<img src="<?php echo $conf['logo_mobile_url'];?>" class="logo-2">
				<?php if($header['mobile_title']) { ?>
					<span class="hidden-lg"><?php echo $header['mobile_title'];?></span>
				<?php } ?>
			</a>
			
			
			
			<?php if(empty($uid)) { ?>
				<a class="navbar-brand hidden-lg" href="<?php echo url('user-login');?>" aria-label="<?php echo lang('login');?>"> <i class="icon-user icon"></i></a>
			<?php } else { ?>
				<a class="navbar-brand hidden-lg" href="<?php echo url("thread-create-$fid");?>" aria-label="<?php echo lang('thread_create');?>"><i class="icon-edit icon"></i></a>
			<?php } ?>
			
			
			
			<div class="collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				<ul class="navbar-nav mr-auto">
					
					<li class="nav-item home" fid="0" data-active="fid-0"><a class="nav-link" href="."><i class="icon-home d-md-none"></i> <?php echo lang('index_page');?></a></li>
					
					<?php foreach($forumlist_show as $_forum) { ?>
					
					<li class="nav-item" fid="<?php echo $_forum['fid'];?>" data-active="fid-<?php echo $_forum['fid'];?>">
						<a class="nav-link" href="<?php echo url("forum-$_forum[fid]");?>"><i class="icon-circle-o d-md-none"></i> <?php echo $_forum['name'];?></a>
					</li>
					
					<?php } ?>
					
				</ul>
				<!-- 右侧：用户 -->
				<ul class="navbar-nav">
					
				<?php if(empty($uid)) { ?>
					<li class="nav-item"><a class="nav-link" href="<?php echo url('user-login');?>"><i class="icon-user"></i> <?php echo lang('login');?></a></li>
					<!--<li class="nav-item"><a class="nav-link" href="<?php echo url('user-create');?>"><?php echo lang('register');?></a></li>-->
				<?php } else { ?>
					<li class="nav-item username"><a class="nav-link" href="<?php echo url('my');?>"><img class="avatar-1" src="<?php echo $user['avatar_url'];?>"> <?php echo $user['username'];?></a></li>
					<!-- 管理员 -->
					<?php if($gid == 1) { ?>
					<li class="nav-item"><a class="nav-link" href="admin/"><i class="icon-home"></i> <?php echo lang('admin_page');?></a></li>
					<?php } ?>
					
					<li class="nav-item"><a class="nav-link" href="<?php echo url('user-logout');?>"><i class="icon-sign-out"></i> <?php echo lang('logout');?></a></li>
				<?php } ?>
					
				</ul>
			</div>
		</div>
	</header>
	