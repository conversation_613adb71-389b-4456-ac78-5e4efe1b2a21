<?php include _include(APP_PATH.'view/htm/header.inc.htm');?>



<div class="row">
	<div class="col-lg-6 mx-auto">
		<div class="card">
			<div class="card-header">
				<?php echo lang('user_login');?>
				
			</div>
			<div class="card-body ajax_modal_body">
				<form action="<?php echo url('user-login');?>" method="post" id="form">
					<div class="form-group input-group">
						<div class="input-group-prepend">
							<span class="input-group-text"><i class="icon icon-user icon-fw"></i></span>
						</div>
						<input type="text" class="form-control" placeholder="<?php echo lang('email');?> / <?php echo lang('username');?>" id="email" name="email">
						<div class="invalid-feedback"></div>
					</div>
					
					<div class="form-group input-group">
						<div class="input-group-prepend">
							<span class="input-group-text"><i class="icon icon-lock icon-fw"></i></span>
						</div>
						<input type="password" class="form-control" placeholder="<?php echo lang('password');?>" id="password" name="password">
						<div class="invalid-feedback"></div>
					</div>
					
					<?php echo csrf_token_field(); ?>
					<div class="form-group">
						<button type="submit" class="btn btn-primary btn-block" id="submit" loading-text="<?php echo lang('submiting');?>..."><?php echo lang('login');?></button>
					</div>
					
					<div class="row mt-3">
						<div class="col-6">
							
							<a href="<?php echo url('user-create');?>" class="btn btn-outline-primary btn-sm">
								<i class="icon icon-user-plus"></i> <?php echo lang('user_create');?>
							</a>
						</div>
						<div class="col-6 text-right">
							
							<?php if(!empty($conf['user_resetpw_on'])) { ?>
							<a href="<?php echo url('user-resetpw');?>" class="text-muted"><small><?php echo lang('forgot_pw');?></small></a>
							<?php } ?>
							
						</div>
					</div>
				</form>
			</div>
		</div>
		
	</div>
</div>



<?php include _include(APP_PATH.'view/htm/footer.inc.htm');?>

<script src="<?php echo $conf['view_url'];?>js/md5.js"></script>

<script>
$(document).ready(function() {
	var jform = $('#form');
	var jsubmit = $('#submit');
	var jemail = $('#email');
	var jpassword = $('#password');
	var referer = '<?php echo $referer;?>';
	var isSubmitting = false; // 防止重复提交

	// 表单提交处理函数
	function submitForm() {
		// 防止重复提交
		if (isSubmitting) {
			return false;
		}

		// 验证表单
		if (!jemail.val().trim()) {
			if (jemail.alert) {
				jemail.alert('请输入邮箱或用户名').focus();
			} else {
				alert('请输入邮箱或用户名');
				jemail.focus();
			}
			return false;
		}

		if (!jpassword.val().trim()) {
			if (jpassword.alert) {
				jpassword.alert('请输入密码').focus();
			} else {
				alert('请输入密码');
				jpassword.focus();
			}
			return false;
		}

		isSubmitting = true;

		// 清除之前的错误提示
		jform.find('.is-invalid').removeClass('is-invalid');
		jform.find('.invalid-feedback').hide();

		// 设置按钮状态
		if (jsubmit.button) {
			jsubmit.button('loading');
		} else {
			jsubmit.prop('disabled', true).text('登录中...');
		}

		var postdata = jform.serializeObject();

		// 不再在前端进行MD5哈希，直接发送原始密码（通过HTTPS保护）
		$.xpost(jform.attr('action'), postdata, function(code, message) {
			isSubmitting = false;

			if(code == 0) {
				if (jsubmit.button) {
					jsubmit.button(message).delay(1000).location(referer);
				} else {
					jsubmit.text(message);
					setTimeout(function() {
						window.location.href = referer;
					}, 1000);
				}
			} else if(xn.is_number && xn.is_number(code)) {
				alert(message);
				if (jsubmit.button) {
					jsubmit.button('reset');
				} else {
					jsubmit.prop('disabled', false).text('<?php echo lang('login');?>');
				}
			} else {
				// 显示字段特定的错误
				var field = jform.find('[name="'+code+'"]');
				if (field.length > 0) {
					field.addClass('is-invalid');
					var feedback = field.next('.invalid-feedback');
					if (feedback.length > 0) {
						feedback.text(message).show();
					}
					field.focus();
				} else {
					alert(message);
				}

				if (jsubmit.button) {
					jsubmit.button('reset');
				} else {
					jsubmit.prop('disabled', false).text('<?php echo lang('login');?>');
				}
			}
		}).fail(function() {
			isSubmitting = false;
			alert('网络错误，请重试');
			if (jsubmit.button) {
				jsubmit.button('reset');
			} else {
				jsubmit.prop('disabled', false).text('<?php echo lang('login');?>');
			}
		});
	}

	// 表单提交事件
	jform.on('submit', function(e) {
		e.preventDefault();
		e.stopPropagation();
		submitForm();
		return false;
	});

	// 按钮点击事件
	jsubmit.on('click', function(e) {
		e.preventDefault();
		e.stopPropagation();
		submitForm();
		return false;
	});

	// 回车键事件
	jemail.on('keydown', function(e) {
		if (e.which == 13 || e.keyCode == 13) { // Enter key
			e.preventDefault();
			e.stopPropagation();
			if (jemail.val().trim()) {
				jpassword.focus();
			} else {
				submitForm();
			}
			return false;
		}
	});

	jpassword.on('keydown', function(e) {
		if (e.which == 13 || e.keyCode == 13) { // Enter key
			e.preventDefault();
			e.stopPropagation();
			submitForm();
			return false;
		}
	});

	// 自动聚焦到第一个空字段
	setTimeout(function() {
		if (!jemail.val()) {
			jemail.focus();
		} else if (!jpassword.val()) {
			jpassword.focus();
		}
	}, 100);
});

</script>

