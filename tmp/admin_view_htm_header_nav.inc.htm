	<?php !isset($user['avatar_url']) AND $user['avatar_url'] = ''; ?>

	
	
	<header class="navbar navbar-expand-md navbar-dark bg-dark" id="header">
		<div class="container">
			<!-- 左：折叠菜单的图标 ☰ -->
			<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#nav" aria-controls="navbar_collapse" aria-expanded="false" aria-label="<?php echo lang('toggler_menu');?>">
				<span class="navbar-toggler-icon"></span>
			</button>
			
			<!-- 中：LOGO -->
			<a class="navbar-brand" href="<?php echo $header['mobile_link'];?>">
				<img src="../view/img/logo.png" class="logo-2">
				<?php if($header['mobile_title']) { ?>
					<span class="d-md-none"><?php echo $header['mobile_title'];?></span>
				<?php } ?>
			</a>
			
			<!--- 右：用户 --->
			<a class="navbar-brand d-md-none" href="../" title="<?php echo lang('front_index_page');?>"><i class="icon-home"></i></a>
			
			
			<div class="collapse navbar-collapse" id="nav">
				<!-- 左侧：版块 -->
				<ul class="navbar-nav mr-auto">
					
						
					<li class="nav-item home">
						<a class="nav-link" href="./"><i class="icon-home"></i> <?php echo lang('admin_index_page');?></a>
					</li>
					<?php foreach ($menu as $k=>$v) { ?>
					<li class="nav-item nav-item-<?php echo $k;?>">
						<a class="nav-link" href="<?php echo $v['url'];?>"><i class="<?php echo $v['icon'];?>"></i> <?php echo $v['text'];?></a>
					</li>
					<?php  } ?>
					
					
				</ul>
				<!-- 右侧：用户 -->
				<ul class="navbar-nav">
					
					
					<li class="nav-item">
						<a class="nav-link" href="../<?php echo url('my');?>">
							<img class="avatar-1" src="<?php echo (strpos($user['avatar_url'], '//') === FALSE ? '../' : '').$user['avatar_url'];?>">
							<?php echo isset($user['username']) ? $user['username'] : '';?>
						</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" href="../"><i class="icon-home"></i> <?php echo lang('front_index_page');?></a>
					</li>
					<li class="nav-item">
						<a class="nav-link" href="<?php echo url('index-logout');?>"><i class="icon-sign-out"></i> <?php echo lang('logout');?></a>
					</li>
					
					
				</ul>
			</div>
		</div>
	</header>
	