{"test_time": "2025-07-05 09:28:46", "total_tests": 20, "passed_tests": 19, "failed_tests": 1, "success_rate": 95, "results": [{"test": "主页访问", "success": true, "message": "主页正常加载，包含Boyou BBS品牌"}, {"test": "HTTP状态", "success": true, "message": "HTTP 200 OK"}, {"test": "框架加载", "success": true, "message": "BoyouPHP 6.1 加载成功"}, {"test": "核心函数", "success": true, "message": "核心函数G()和param()可用"}, {"test": "资源: Bootstrap CSS", "success": true, "message": "正常加载"}, {"test": "资源: BBS样式", "success": true, "message": "正常加载"}, {"test": "资源: j<PERSON><PERSON>y库", "success": true, "message": "正常加载"}, {"test": "资源: Bootstrap JS", "success": true, "message": "正常加载"}, {"test": "资源: <PERSON><PERSON>框架JS", "success": true, "message": "正常加载"}, {"test": "资源: 网站Logo", "success": true, "message": "正常加载"}, {"test": "资源: 网站图标", "success": true, "message": "正常加载"}, {"test": "页面: 首页", "success": true, "message": "HTML结构完整"}, {"test": "页面: 管理后台", "success": true, "message": "HTML结构完整"}, {"test": "页面: 安装页面", "success": false, "message": "HTML结构不完整"}, {"test": "数据库文件", "success": true, "message": "SQLite数据库文件存在"}, {"test": "数据库连接", "success": true, "message": "数据库连接正常，包含5个表"}, {"test": "配置: 主配置文件", "success": true, "message": "文件存在"}, {"test": "配置内容", "success": true, "message": "配置文件格式正确，站点名: Boyou BBS"}, {"test": "配置: 默认配置文件", "success": true, "message": "文件存在"}, {"test": "配置: 安装锁文件", "success": true, "message": "文件存在"}]}