<?php include _include(APP_PATH.'view/htm/header.inc.htm');?>

<!--{hook user_create_start.htm}-->

<div class="row">
	<div class="col-lg-6 mx-auto">
		<!--{hook user_create_card_before.htm}-->
		<div class="card">
			<div class="card-header">
				<?php echo lang('user_create');?>
				<!--{hook user_create_title_after.htm}-->
			</div>
			<div class="card-body">
				<form action="<?php echo url('user-create');?>" method="post" id="form">
				
					<div class="form-group input-group">
						<div class="input-group-prepend">
							<span class="input-group-text"><i class="icon icon-envelope icon-fw"></i></span>
						</div>
						<input type="email" class="form-control" placeholder="<?php echo lang('email');?>" name="email" id="email" required>
					</div>
					
					<!--{hook user_create_email_after.htm}-->
					
					<div class="form-group input-group">
						<div class="input-group-prepend">
							<span class="input-group-text"><i class="icon icon-user icon-fw"></i></span>
						</div>
						<input type="text" class="form-control" placeholder="<?php echo lang('username');?>" name="username" id="username">
					</div>
					
					<!--{hook user_create_username_after.htm}-->
					
					<div class="media">
						<div class="media-body">
							<div class="form-group input-group">
								<div class="input-group-prepend">
									<span class="input-group-text"><i class="icon icon-lock icon-fw"></i></span>
								</div>
								<input type="password" class="form-control" placeholder="<?php echo lang('password');?>" name="password" id="password">
							</div>
						</div>
					</div>
					
					<!--{hook user_create_password_after.htm}-->
					
					
					<?php if($conf['user_create_email_on']) { ?>
					<div class="media">
						<div class="media-body">
							<div class="form-group input-group">
								<div class="input-group-prepend">
									<span class="input-group-text"><i class="icon icon-barcode icon-fw"></i></span>
								</div>
								<input type="text" class="form-control" placeholder="<?php echo lang('verify_code');?>" name="code" id="code">
							</div>
						</div>
						<div class="align-self-center ml-1">
							<button type="submit" class="btn btn-primary btn-sm ml-3 form-group" id="sendcode" loading-text="<?php echo lang('sending');?>..." action="<?php echo url('user-send_code-user_create');?>"><?php echo lang('send_verify_code');?></button>
						</div>
					</div>
					<?php } ?>

					<?php echo csrf_token_field(); ?>
					<div class="form-group">
						<button type="submit" class="btn btn-primary btn-block" id="submit" loading-text="<?php echo lang('submiting');?>..." <?php if($conf['user_create_email_on']) { ?>disabled<?php } ?>><?php echo lang('next_step');?></button>
					</div>
					<!--{hook user_create_submit_after.htm}-->
					
					<div class="media">
						<div>
							<!--{hook user_create_form_footer_left.htm}-->
						</div>
						<div class="media-body text-right">
							<!--{hook user_create_form_footer_right_start.htm}-->
							<a href="<?php echo url('user-login');?>" class="text-muted"><small><?php echo lang('user_login');?></small></a>
							<?php if(!empty($conf['user_resetpw_on'])) { ?>
							<a href="<?php echo url('user-resetpw');?>" class="text-muted ml-3"><small><?php echo lang('forgot_pw');?></small></a>
							<?php } ?>
							<!--{hook user_create_form_footer_right_end.htm}-->
						</div>
					</div>
					
				</form>
			</div>
		</div>
		<!--{hook user_create_card_after.htm}-->
	</div>
</div>

<!--{hook user_create_end.htm}-->

<?php include _include(APP_PATH.'view/htm/footer.inc.htm');?>

<script src="<?php echo $conf['view_url'];?>js/md5.js"></script>

<script>
var jform = $('#form');
var jsubmit = $('#submit');
var jsend = $('#sendcode');
var referer = '<?php echo http_referer();?>';
jsend.on('click', function() {
	jform.reset();
	jsend.button('loading');
	var postdata = jform.serialize();
	$.xpost(jsend.attr('action'), postdata, function(code, message) {
		if(code == 0) {
			$('#code').focus();
			var t = 60; // 倒计时
			jsend.button('<?php echo lang('user_send_sucessfully');?> 60 ');
			jsubmit.button('reset');
			// 倒计时，重新发送
			var handler = setInterval(function() {
				jsend.button('<?php echo lang('user_send_sucessfully');?> '+(--t)+' ');
				if(t == 0) {
					clearInterval(handler);
					jsend.button('reset');
				}
			}, 1000);
		} else if(code < 0) {
			$.alert(message, -1);
			jsend.button('reset');
		} else {
			jform.find('[name="'+code+'"]').alert(message).focus();
			jsend.button('reset');
		}
	});
	return false;
});

// 表单提交处理函数
function submitRegisterForm() {
	var postdata = jform.serializeObject();

	// 基本验证
	if (!postdata.email || !postdata.username || !postdata.password) {
		alert('请填写完整的注册信息');
		return false;
	}

	jsubmit.button('loading');

	// 不再在前端进行MD5哈希，直接发送原始密码
	$.xpost(jform.attr('action'), postdata, function(code, message) {
		if(code == 0) {
			jsubmit.button(message).delay(1000).location(referer);
		} else if(xn.is_number(code)) {
			alert(message);
			jsubmit.button('reset');
		} else {
			var field = jform.find('[name="'+code+'"]');
			if (field.length > 0) {
				field.alert(message).focus();
			} else {
				alert(message);
			}
			jsubmit.button('reset');
		}
	});
}

// 表单提交事件
jform.on('submit', function(e) {
	e.preventDefault();
	submitRegisterForm();
	return false;
});

// 按钮点击事件
jsubmit.on('click', function(e) {
	e.preventDefault();
	submitRegisterForm();
	return false;
});

</script>

<!--{hook user_create_js.htm}-->
<script src="<?php echo $conf['static_url'];?>view/js/password-strength.js?v=<?php echo $conf['version'];?>"></script>