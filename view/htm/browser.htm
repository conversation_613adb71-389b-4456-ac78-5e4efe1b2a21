<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>提示信息 / Information</title>
<style type="text/css">
a {color:#d4557f; font-size: 12px; color: black;}
a:hover {color:#a01845}
.main{width:1000px; height:350px; margin:20px auto; background:url(view/img/browser.gif) no-repeat; position:relative}
.pos1{position:absolute; top:300px; left:380px}
.pos2{position:absolute; top:300px; left:570px}
.pos3{position:absolute; top:300px; left:760px}
</style>
</head>
<body>
<div class="main">
	<a class="pos1" href="https://www.google.com/chrome/" target="_blank">下载/Download Chrome</a>
	<a class="pos2" href="https://www.mozilla.org/firefox/" target="_blank">下载/Download Firefox</a>
	<a class="pos3" href="https://www.microsoft.com/edge" target="_blank">下载/Download Edge</a>
</div>
<br>
<p style="font-size: 12px; text-align: center; color: #888888;">您的浏览器信息 / Your browser information：<script>document.write(navigator.userAgent);</script></p>
</body>
</html>