<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $conf['sitename'] ?? 'Boyou BBS 6.1'; ?> - 现代化论坛系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 2em;
            font-weight: bold;
            text-decoration: none;
            color: white;
        }
        .nav {
            display: flex;
            gap: 20px;
        }
        .nav a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px;
            transition: background 0.3s;
        }
        .nav a:hover {
            background: rgba(255,255,255,0.2);
        }
        .main-content {
            margin: 30px auto;
            max-width: 1200px;
            padding: 0 20px;
        }
        .welcome-banner {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .welcome-banner h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        .welcome-banner p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 5px solid #667eea;
        }
        .stat-card h3 {
            font-size: 2em;
            color: #667eea;
            margin-bottom: 10px;
        }
        .stat-card p {
            color: #666;
            font-size: 1.1em;
        }
        .forum-list {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        .forum-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .forum-header h2 {
            color: #333;
            font-size: 1.5em;
        }
        .forum-item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s;
        }
        .forum-item:hover {
            background: #f8f9fa;
        }
        .forum-item:last-child {
            border-bottom: none;
        }
        .forum-info h3 {
            color: #667eea;
            margin-bottom: 5px;
        }
        .forum-info p {
            color: #666;
            font-size: 0.9em;
        }
        .forum-stats {
            text-align: right;
            color: #666;
        }
        .forum-stats div {
            margin-bottom: 5px;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.2s;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 30px 0;
            margin-top: 50px;
        }
        .footer p {
            margin-bottom: 10px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .feature-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        .feature-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        .feature-card p {
            color: #666;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">🚀 <?php echo $conf['sitename'] ?? 'Boyou BBS'; ?></a>
                <nav class="nav">
                    <a href="/">首页</a>
                    <a href="?action=login">登录</a>
                    <a href="?action=register">注册</a>
                    <a href="admin/">管理</a>
                </nav>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="welcome-banner">
            <h1>🎉 欢迎来到 <?php echo $conf['sitename'] ?? 'Boyou BBS'; ?></h1>
            <p>现代化论坛系统，基于 BoyouPHP 6.1 框架 - 为社区交流而生</p>
            <div style="margin-top: 20px;">
                <a href="?action=register" class="btn">立即注册</a>
                <a href="?action=login" class="btn btn-outline">用户登录</a>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>1</h3>
                <p>版块数量</p>
            </div>
            <div class="stat-card">
                <h3>1</h3>
                <p>注册用户</p>
            </div>
            <div class="stat-card">
                <h3>0</h3>
                <p>主题数量</p>
            </div>
            <div class="stat-card">
                <h3>0</h3>
                <p>回复数量</p>
            </div>
        </div>

        <div class="forum-list">
            <div class="forum-header">
                <h2>📋 论坛版块</h2>
            </div>
            <div class="forum-item">
                <div class="forum-info">
                    <h3>默认版块</h3>
                    <p>这是系统默认创建的版块，您可以在管理后台进行修改</p>
                </div>
                <div class="forum-stats">
                    <div><strong>0</strong> 主题</div>
                    <div><strong>0</strong> 回复</div>
                </div>
            </div>
        </div>

        <div class="features">
            <div class="feature-card">
                <div class="icon">🎨</div>
                <h3>现代化设计</h3>
                <p>采用最新的设计理念，提供优雅的用户体验</p>
            </div>
            <div class="feature-card">
                <div class="icon">🛡️</div>
                <h3>企业级安全</h3>
                <p>内置CSRF保护、XSS防护等多重安全机制</p>
            </div>
            <div class="feature-card">
                <div class="icon">⚡</div>
                <h3>高性能</h3>
                <p>支持多种缓存方案，确保系统高速运行</p>
            </div>
            <div class="feature-card">
                <div class="icon">📱</div>
                <h3>响应式设计</h3>
                <p>完美适配PC、平板、手机等各种设备</p>
            </div>
            <div class="feature-card">
                <div class="icon">🔧</div>
                <h3>易于扩展</h3>
                <p>强大的插件系统，支持自定义功能开发</p>
            </div>
            <div class="feature-card">
                <div class="icon">🌐</div>
                <h3>SEO友好</h3>
                <p>内置SEO优化功能，提升搜索引擎排名</p>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p><strong>Boyou BBS 6.1</strong> &copy; 2024 | 基于 BoyouPHP 6.1 框架</p>
            <p>现代化论坛系统 - 版本 <?php echo $conf['version'] ?? '6.1.0'; ?></p>
            <p style="margin-top: 15px; opacity: 0.8;">
                🚀 安装成功！系统运行正常 | 
                <a href="health_check.php" style="color: #4facfe;">健康检查</a> | 
                <a href="admin/" style="color: #4facfe;">管理后台</a>
            </p>
        </div>
    </footer>

    <script src="view/js/boyou.js"></script>
    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Boyou BBS 6.1 加载完成');
            
            // 添加一些动画效果
            const cards = document.querySelectorAll('.stat-card, .feature-card, .forum-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
