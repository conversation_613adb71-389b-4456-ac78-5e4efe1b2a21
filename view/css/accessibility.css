
/* 无障碍访问 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: white;
    padding: 8px;
    text-decoration: none;
    z-index: 10000;
}

.skip-link:focus {
    top: 6px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid;
    }
    
    a {
        text-decoration: underline;
    }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 焦点样式 */
:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus,
select:focus {
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}
