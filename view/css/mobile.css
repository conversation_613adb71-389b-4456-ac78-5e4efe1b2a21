
/* 移动端优化 */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .btn {
        padding: 12px 20px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    input, textarea, select {
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .table-responsive {
        border: none;
    }
    
    .navbar-collapse {
        background: white;
        padding: 10px;
        border-radius: 4px;
        margin-top: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .modal-dialog {
        margin: 10px;
    }
}

/* 触摸友好 */
.touch-friendly {
    min-height: 44px;
    min-width: 44px;
}

/* 手势支持 */
.swipe-item {
    touch-action: pan-y;
}
