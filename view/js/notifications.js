
// 通知系统
window.Notification = (function() {
    "use strict";
    
    function show(message, type, duration) {
        type = type || "info";
        duration = duration || 5000;
        
        const notification = document.createElement("div");
        notification.className = "notification " + type;
        notification.innerHTML = message + "<button class=\"close\">&times;</button>";
        
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(function() {
            notification.classList.add("show");
        }, 100);
        
        // 关闭按钮
        notification.querySelector(".close").addEventListener("click", function() {
            hide(notification);
        });
        
        // 自动关闭
        if (duration > 0) {
            setTimeout(function() {
                hide(notification);
            }, duration);
        }
        
        return notification;
    }
    
    function hide(notification) {
        notification.classList.remove("show");
        setTimeout(function() {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    return {
        success: function(message, duration) {
            return show(message, "success", duration);
        },
        error: function(message, duration) {
            return show(message, "error", duration);
        },
        warning: function(message, duration) {
            return show(message, "warning", duration);
        },
        info: function(message, duration) {
            return show(message, "info", duration);
        }
    };
})();

// 按钮加载状态
window.ButtonLoader = {
    start: function(button) {
        button.classList.add("loading");
        button.disabled = true;
    },
    stop: function(button) {
        button.classList.remove("loading");
        button.disabled = false;
    }
};
