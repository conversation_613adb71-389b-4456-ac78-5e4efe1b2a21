
// 表单验证增强
(function() {
    "use strict";
    
    // 实时密码强度检查
    function checkPasswordStrength(password) {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        
        return strength;
    }
    
    // 显示密码强度
    function showPasswordStrength(input) {
        const strength = checkPasswordStrength(input.value);
        const indicator = input.parentNode.querySelector(".password-strength");
        
        if (indicator) {
            const levels = ["很弱", "弱", "一般", "强", "很强"];
            const colors = ["#ff4757", "#ff6b7a", "#ffa502", "#2ed573", "#1e90ff"];
            
            indicator.textContent = "密码强度: " + (levels[strength - 1] || "很弱");
            indicator.style.color = colors[strength - 1] || colors[0];
        }
    }
    
    // 邮箱格式验证
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    // 用户名验证
    function validateUsername(username) {
        return /^[a-zA-Z0-9_]{3,20}$/.test(username);
    }
    
    // 初始化表单验证
    document.addEventListener("DOMContentLoaded", function() {
        // 密码强度检查
        const passwordInputs = document.querySelectorAll("input[type=password]");
        passwordInputs.forEach(function(input) {
            if (input.name === "password" || input.name === "new_password") {
                const strengthDiv = document.createElement("div");
                strengthDiv.className = "password-strength";
                input.parentNode.appendChild(strengthDiv);
                
                input.addEventListener("input", function() {
                    showPasswordStrength(this);
                });
            }
        });
        
        // 邮箱验证
        const emailInputs = document.querySelectorAll("input[type=email]");
        emailInputs.forEach(function(input) {
            input.addEventListener("blur", function() {
                if (this.value && !validateEmail(this.value)) {
                    this.setCustomValidity("请输入有效的邮箱地址");
                } else {
                    this.setCustomValidity("");
                }
            });
        });
        
        // 用户名验证
        const usernameInputs = document.querySelectorAll("input[name=username]");
        usernameInputs.forEach(function(input) {
            input.addEventListener("blur", function() {
                if (this.value && !validateUsername(this.value)) {
                    this.setCustomValidity("用户名只能包含字母、数字和下划线，长度3-20位");
                } else {
                    this.setCustomValidity("");
                }
            });
        });
    });
})();
