/**
 * 密码强度检查和用户教育
 */

// 密码强度检查函数
function checkPasswordStrength(password) {
    var score = 0;
    var feedback = [];
    
    // 长度检查
    if (password.length >= 8) {
        score += 1;
    } else {
        feedback.push('密码长度至少8位');
    }
    
    if (password.length >= 12) {
        score += 1;
    }
    
    // 包含小写字母
    if (/[a-z]/.test(password)) {
        score += 1;
    } else {
        feedback.push('包含小写字母');
    }
    
    // 包含大写字母
    if (/[A-Z]/.test(password)) {
        score += 1;
    } else {
        feedback.push('包含大写字母');
    }
    
    // 包含数字
    if (/[0-9]/.test(password)) {
        score += 1;
    } else {
        feedback.push('包含数字');
    }
    
    // 包含特殊字符
    if (/[^A-Za-z0-9]/.test(password)) {
        score += 1;
    } else {
        feedback.push('包含特殊字符(!@#$%^&*等)');
    }
    
    // 避免常见密码
    var commonPasswords = ['123456', 'password', '123456789', '12345678', '12345', '1234567', 'admin', 'qwerty'];
    if (commonPasswords.indexOf(password.toLowerCase()) !== -1) {
        score = Math.max(0, score - 2);
        feedback.push('避免使用常见密码');
    }
    
    var strength = '';
    var className = '';
    
    if (score <= 2) {
        strength = '弱';
        className = 'text-danger';
    } else if (score <= 4) {
        strength = '中等';
        className = 'text-warning';
    } else {
        strength = '强';
        className = 'text-success';
    }
    
    return {
        score: score,
        strength: strength,
        className: className,
        feedback: feedback
    };
}

// 显示密码强度
function showPasswordStrength(inputElement, displayElement) {
    var password = inputElement.val();
    var result = checkPasswordStrength(password);
    
    var html = '<div class="password-strength mt-2">';
    html += '<div class="d-flex justify-content-between align-items-center">';
    html += '<span>密码强度: <span class="' + result.className + '">' + result.strength + '</span></span>';
    html += '<div class="progress flex-grow-1 ml-2" style="height: 8px;">';
    html += '<div class="progress-bar ' + (result.className.replace('text-', 'bg-')) + '" style="width: ' + (result.score * 16.67) + '%"></div>';
    html += '</div>';
    html += '</div>';
    
    if (result.feedback.length > 0) {
        html += '<div class="small text-muted mt-1">建议: ' + result.feedback.join('、') + '</div>';
    }
    
    html += '</div>';
    
    displayElement.html(html);
}

// 安全提示
function showSecurityTips() {
    return `
    <div class="alert alert-info mt-3">
        <h6><i class="icon-shield"></i> 安全提示</h6>
        <ul class="mb-0 small">
            <li>使用至少8位字符，包含大小写字母、数字和特殊字符</li>
            <li>不要使用个人信息（姓名、生日、电话等）作为密码</li>
            <li>不要在多个网站使用相同密码</li>
            <li>定期更换密码，建议3-6个月更换一次</li>
            <li>不要在公共场所输入密码</li>
        </ul>
    </div>
    `;
}

// 初始化密码强度检查
function initPasswordStrength() {
    // 为所有密码输入框添加强度检查
    $('input[type="password"][name="password"], input[type="password"][name="password_new"]').each(function() {
        var $input = $(this);
        var $container = $input.closest('.form-group');
        
        // 添加强度显示容器
        if ($container.find('.password-strength-display').length === 0) {
            $container.append('<div class="password-strength-display"></div>');
        }
        
        var $display = $container.find('.password-strength-display');
        
        // 绑定输入事件
        $input.on('input keyup', function() {
            if ($(this).val().length > 0) {
                showPasswordStrength($(this), $display);
            } else {
                $display.empty();
            }
        });
    });
    
    // 在注册和修改密码页面添加安全提示
    if (window.location.href.indexOf('user-create') !== -1 || 
        window.location.href.indexOf('my-password') !== -1) {
        $('.card-body form').append(showSecurityTips());
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    initPasswordStrength();
});
