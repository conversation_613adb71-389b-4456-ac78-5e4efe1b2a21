/*
* boyou.js 封装了部分 PHP 常用的函数，便于代码移植和使用。
* 基于 boyou.js 进行现代化改造
*/

/********************* 对 window 对象进行扩展 ************************/
// 兼容 ie89
if(!Object.keys) {
	Object.keys = function(o) {
		var arr = [];
		for(var k in o) {
			if(o.hasOwnProperty(k)) arr.push(k);
		}
		return arr;
	}
}
if(!Object.values) {
	Object.values = function(o) {
		var arr = [];
		if(!o) return arr;
		for(var k in o) {
			if(o.hasOwnProperty(k)) arr.push(o[k]);
		}
		return arr;
	}
}
Array.values = function(arr) {
	return xn.array_filter(arr);
};

Object.first = function(obj) {
	for(var k in obj) return obj[k];
};
Object.last = function(obj) {
	for(var k in obj);
	return obj[k];
};
Object.length = function(obj) {
	var n = 0;
	for(var k in obj) n++;
	return n;
};
Object.count = function(obj) {
	if(!obj) return 0;
	if(obj.length) return obj.length;
	var n = 0;
	for(k in obj) {
		if(obj.hasOwnProperty(k)) n++;
	}
	return n;
};
Object.sum = function(obj) {
	var sum = 0;
	$.each(obj, function(k, v) {sum += intval(v)});
	return sum;
};
if(typeof console == 'undefined') {
	console = {};
	console.log = function() {};
}

/********************* xn 模拟 php 函数 ************************/

// var xn = window; // browser， 如果要兼容以前的版本，请开启这里。
// var xn = global; // nodejs
var xn = {}; // 避免冲突，自己的命名空间。

// 针对国内的山寨套壳浏览器检测不准确
xn.is_ie = (!!document.all) ? true : false;// ie6789
xn.is_ie_10 = navigator.userAgent.indexOf('Trident') != -1;
xn.is_ff = navigator.userAgent.indexOf('Firefox') != -1;
xn.in_mobile = ($(window).width() < 1140);
xn.options = {}; // 全局配置
xn.options.water_image_url = 'view/img/water-small.png';// 默认水印路径

xn.htmlspecialchars = function(s) {
	s = s.replace(/</g, "&lt;");
	s = s.replace(/>/g, "&gt;");
	return s;
};

// 标准的 urlencode()
xn._urlencode = function(s) {
	s = encodeURIComponent(s);
	s = xn.strtolower(s);
	return s;
};

// 标准的 urldecode()
xn._urldecode = function(s) {
	s = decodeURIComponent(s);
	return s;
};

xn.urlencode = function(s) {
	s = encodeURIComponent(s);
	s = s.replace(/_/g, "%5f");
	s = s.replace(/\-/g, "%2d");
	s = s.replace(/\./g, "%2e");
	s = s.replace(/\~/g, "%7e");
	s = xn.strtolower(s);
	return s;
};

xn.urldecode = function(s) {
	s = s.replace(/\+/g, ' ');
	s = decodeURIComponent(s);
	return s;
};

// 标准的 rawurlencode()
xn.rawurlencode = function(s) {
	s = encodeURIComponent(s);
	return s;
};

// 标准的 rawurldecode()
xn.rawurldecode = function(s) {
	s = decodeURIComponent(s);
	return s;
};

xn.base64_encode = function(s) {
	return btoa(s);
};

xn.base64_decode = function(s) {
	return atob(s);
};

xn.md5 = function(s) {
	return hex_md5(s);
};

xn.time = function() {
	return Math.round(new Date().getTime() / 1000);
};

xn.microtime = function() {
	return new Date().getTime() / 1000;
};

// JSON编码解码
xn.json_encode = function(obj) {
	return JSON.stringify(obj);
};

xn.json_decode = function(str) {
	try {
		return JSON.parse(str);
	} catch(e) {
		return null;
	}
};

// 字符串函数
xn.strlen = function(s) {
	return s.length;
};

xn.substr = function(s, start, length) {
	if(typeof length === 'undefined') {
		return s.substring(start);
	}
	return s.substring(start, start + length);
};

xn.strpos = function(haystack, needle, offset) {
	offset = offset || 0;
	return haystack.indexOf(needle, offset);
};

xn.strtolower = function(s) {
	return s.toLowerCase();
};

xn.strtoupper = function(s) {
	return s.toUpperCase();
};

xn.trim = function(s) {
	return s.replace(/^\s+|\s+$/g, '');
};

xn.ltrim = function(s) {
	return s.replace(/^\s+/, '');
};

xn.rtrim = function(s) {
	return s.replace(/\s+$/, '');
};

// 数组函数
xn.array_merge = function() {
	var result = [];
	for(var i = 0; i < arguments.length; i++) {
		if(Array.isArray(arguments[i])) {
			result = result.concat(arguments[i]);
		}
	}
	return result;
};

xn.array_keys = function(obj) {
	return Object.keys(obj);
};

xn.array_values = function(obj) {
	return Object.values(obj);
};

xn.in_array = function(needle, haystack) {
	return haystack.indexOf(needle) !== -1;
};

xn.array_filter = function(arr, callback) {
	if(typeof callback === 'undefined') {
		return arr.filter(function(item) {
			return !!item;
		});
	}
	return arr.filter(callback);
};

// 数学函数
xn.intval = function(val) {
	return parseInt(val, 10) || 0;
};

xn.floatval = function(val) {
	return parseFloat(val) || 0;
};

xn.abs = function(val) {
	return Math.abs(val);
};

xn.max = function() {
	return Math.max.apply(null, arguments);
};

xn.min = function() {
	return Math.min.apply(null, arguments);
};

xn.rand = function(min, max) {
	if(typeof min === 'undefined') min = 0;
	if(typeof max === 'undefined') max = 32767;
	return Math.floor(Math.random() * (max - min + 1)) + min;
};

// 实用函数
xn.empty = function(val) {
	return val === '' || val === 0 || val === '0' || val === null || val === false || typeof val === 'undefined' || (Array.isArray(val) && val.length === 0);
};

xn.isset = function(val) {
	return typeof val !== 'undefined' && val !== null;
};

xn.is_numeric = function(val) {
	return !isNaN(parseFloat(val)) && isFinite(val);
};

xn.is_array = function(val) {
	return Array.isArray(val);
};

xn.is_object = function(val) {
	return typeof val === 'object' && val !== null && !Array.isArray(val);
};

xn.is_string = function(val) {
	return typeof val === 'string';
};

xn.is_function = function(val) {
	return typeof val === 'function';
};

// 输出最终加载信息
console.log('boyou.js loaded');
