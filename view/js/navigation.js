
// 导航增强
(function() {
    "use strict";
    
    // 返回顶部功能
    function initBackToTop() {
        const backToTopBtn = document.createElement("button");
        backToTopBtn.className = "back-to-top";
        backToTopBtn.innerHTML = "↑";
        backToTopBtn.title = "返回顶部";
        document.body.appendChild(backToTopBtn);
        
        window.addEventListener("scroll", function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add("show");
            } else {
                backToTopBtn.classList.remove("show");
            }
        });
        
        backToTopBtn.addEventListener("click", function() {
            window.scrollTo({
                top: 0,
                behavior: "smooth"
            });
        });
    }
    
    // 活动导航项高亮
    function highlightActiveNav() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll("nav a, .nav a");
        
        navLinks.forEach(function(link) {
            if (link.getAttribute("href") === currentPath) {
                link.classList.add("active");
            }
        });
    }
    
    // 初始化
    document.addEventListener("DOMContentLoaded", function() {
        initBackToTop();
        highlightActiveNav();
    });
})();
