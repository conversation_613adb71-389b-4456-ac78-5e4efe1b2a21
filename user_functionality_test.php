<?php

echo "=== Boyou BBS 6.1 用户功能测试 ===\n\n";

class UserFunctionalityTester {
    private $baseUrl = 'http://localhost:8000';
    private $testResults = [];
    private $testUser;
    
    public function runAllTests() {
        // 初始化测试用户数据
        $this->testUser = [
            'username' => 'testuser_' . time(),
            'password' => 'test123456',
            'email' => '<EMAIL>'
        ];

        echo "开始用户功能测试...\n\n";
        
        $this->testUserRegistration();
        $this->testUserLogin();
        $this->testUserProfile();
        $this->testUserPermissions();
        $this->testPasswordSecurity();
        $this->testSessionManagement();
        $this->testUserInteractions();
        
        $this->generateReport();
    }
    
    private function testUserRegistration() {
        echo "1. 测试用户注册功能...\n";
        
        // 测试注册页面访问
        $registerPage = $this->makeRequest('/user.php?action=register');
        if ($registerPage && strpos($registerPage, 'register') !== false) {
            $this->addResult('注册页面访问', true, '注册页面正常加载');
        } else {
            $this->addResult('注册页面访问', false, '注册页面无法访问');
            return;
        }
        
        // 测试注册表单验证
        $this->testRegistrationValidation();
        
        // 测试实际注册流程
        $this->testActualRegistration();
    }
    
    private function testRegistrationValidation() {
        // 测试空用户名
        $response = $this->submitRegistration('', $this->testUser['password'], $this->testUser['email']);
        if ($this->containsError($response, 'username')) {
            $this->addResult('用户名验证', true, '空用户名被正确拒绝');
        } else {
            $this->addResult('用户名验证', false, '用户名验证失败');
        }
        
        // 测试短密码
        $response = $this->submitRegistration($this->testUser['username'], '123', $this->testUser['email']);
        if ($this->containsError($response, 'password')) {
            $this->addResult('密码验证', true, '短密码被正确拒绝');
        } else {
            $this->addResult('密码验证', false, '密码验证失败');
        }
        
        // 测试无效邮箱
        $response = $this->submitRegistration($this->testUser['username'], $this->testUser['password'], 'invalid-email');
        if ($this->containsError($response, 'email')) {
            $this->addResult('邮箱验证', true, '无效邮箱被正确拒绝');
        } else {
            $this->addResult('邮箱验证', false, '邮箱验证失败');
        }
    }
    
    private function testActualRegistration() {
        // 尝试正常注册
        $response = $this->submitRegistration(
            $this->testUser['username'], 
            $this->testUser['password'], 
            $this->testUser['email']
        );
        
        if ($response && (strpos($response, 'success') !== false || strpos($response, '成功') !== false)) {
            $this->addResult('用户注册', true, '用户注册成功');
        } else {
            $this->addResult('用户注册', false, '用户注册失败');
        }
    }
    
    private function testUserLogin() {
        echo "2. 测试用户登录功能...\n";
        
        // 测试登录页面
        $loginPage = $this->makeRequest('/user.php?action=login');
        if ($loginPage && strpos($loginPage, 'login') !== false) {
            $this->addResult('登录页面访问', true, '登录页面正常加载');
        } else {
            $this->addResult('登录页面访问', false, '登录页面无法访问');
            return;
        }
        
        // 测试错误登录
        $response = $this->submitLogin('wronguser', 'wrongpass');
        if ($this->containsError($response, 'login') || $this->containsError($response, 'password')) {
            $this->addResult('错误登录验证', true, '错误登录被正确拒绝');
        } else {
            $this->addResult('错误登录验证', false, '错误登录验证失败');
        }
        
        // 测试正确登录
        $response = $this->submitLogin($this->testUser['username'], $this->testUser['password']);
        if ($response && (strpos($response, 'success') !== false || strpos($response, '登录') !== false)) {
            $this->addResult('用户登录', true, '用户登录成功');
        } else {
            $this->addResult('用户登录', false, '用户登录失败');
        }
    }
    
    private function testUserProfile() {
        echo "3. 测试用户资料功能...\n";
        
        // 测试个人资料页面
        $profilePage = $this->makeRequest('/user.php?action=profile');
        if ($profilePage && strpos($profilePage, 'profile') !== false) {
            $this->addResult('个人资料页面', true, '个人资料页面正常');
        } else {
            $this->addResult('个人资料页面', false, '个人资料页面异常');
        }
        
        // 测试用户设置页面
        $settingPage = $this->makeRequest('/user.php?action=setting');
        if ($settingPage && strpos($settingPage, 'setting') !== false) {
            $this->addResult('用户设置页面', true, '用户设置页面正常');
        } else {
            $this->addResult('用户设置页面', false, '用户设置页面异常');
        }
    }
    
    private function testUserPermissions() {
        echo "4. 测试用户权限功能...\n";
        
        // 测试游客权限
        $guestResponse = $this->makeRequest('/post.php?action=newthread&fid=1');
        if ($this->requiresLogin($guestResponse)) {
            $this->addResult('游客权限控制', true, '游客正确被要求登录');
        } else {
            $this->addResult('游客权限控制', false, '游客权限控制失败');
        }
        
        // 测试普通用户权限
        $userResponse = $this->makeRequestWithAuth('/admin/');
        if ($this->requiresAdmin($userResponse)) {
            $this->addResult('普通用户权限', true, '普通用户无法访问管理后台');
        } else {
            $this->addResult('普通用户权限', false, '普通用户权限控制失败');
        }
    }
    
    private function testPasswordSecurity() {
        echo "5. 测试密码安全功能...\n";
        
        // 测试密码加密存储
        if ($this->checkPasswordEncryption()) {
            $this->addResult('密码加密', true, '密码已加密存储');
        } else {
            $this->addResult('密码加密', false, '密码可能未加密');
        }
        
        // 测试密码重置功能
        $resetPage = $this->makeRequest('/user.php?action=findpwd');
        if ($resetPage && strpos($resetPage, 'reset') !== false) {
            $this->addResult('密码重置页面', true, '密码重置页面可访问');
        } else {
            $this->addResult('密码重置页面', false, '密码重置页面不可用');
        }
    }
    
    private function testSessionManagement() {
        echo "6. 测试会话管理功能...\n";
        
        // 测试会话保持
        $response1 = $this->makeRequestWithAuth('/user.php?action=profile');
        $response2 = $this->makeRequestWithAuth('/user.php?action=setting');
        
        if ($response1 && $response2 && !$this->requiresLogin($response1) && !$this->requiresLogin($response2)) {
            $this->addResult('会话保持', true, '用户会话正常保持');
        } else {
            $this->addResult('会话保持', false, '会话管理有问题');
        }
        
        // 测试登出功能
        $logoutResponse = $this->makeRequest('/user.php?action=logout');
        if ($logoutResponse) {
            $this->addResult('用户登出', true, '登出功能正常');
        } else {
            $this->addResult('用户登出', false, '登出功能异常');
        }
    }
    
    private function testUserInteractions() {
        echo "7. 测试用户交互功能...\n";
        
        // 测试用户列表
        $userListPage = $this->makeRequest('/user.php');
        if ($userListPage && strpos($userListPage, 'user') !== false) {
            $this->addResult('用户列表', true, '用户列表页面正常');
        } else {
            $this->addResult('用户列表', false, '用户列表页面异常');
        }
        
        // 测试在线用户
        $onlineUsersPage = $this->makeRequest('/misc.php?action=online');
        if ($onlineUsersPage && strpos($onlineUsersPage, 'online') !== false) {
            $this->addResult('在线用户', true, '在线用户功能正常');
        } else {
            $this->addResult('在线用户', false, '在线用户功能异常');
        }
    }
    
    private function makeRequest($path, $postData = null) {
        $url = $this->baseUrl . $path;
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => $postData ? 'POST' : 'GET',
                'header' => $postData ? "Content-Type: application/x-www-form-urlencoded\r\n" : '',
                'content' => $postData ? http_build_query($postData) : ''
            ]
        ]);
        
        return @file_get_contents($url, false, $context);
    }
    
    private function makeRequestWithAuth($path) {
        // 简化的认证请求，实际应该包含session或cookie
        return $this->makeRequest($path);
    }
    
    private function submitRegistration($username, $password, $email) {
        return $this->makeRequest('/user.php?action=register', [
            'username' => $username,
            'password' => $password,
            'email' => $email,
            'submit' => '注册'
        ]);
    }
    
    private function submitLogin($username, $password) {
        return $this->makeRequest('/user.php?action=login', [
            'username' => $username,
            'password' => $password,
            'submit' => '登录'
        ]);
    }
    
    private function containsError($response, $field) {
        if (!$response) return false;
        
        $errorPatterns = [
            'error', '错误', 'invalid', '无效', 'required', '必填',
            'too short', '太短', 'too long', '太长', 'exists', '已存在'
        ];
        
        foreach ($errorPatterns as $pattern) {
            if (stripos($response, $pattern) !== false) {
                return true;
            }
        }
        return false;
    }
    
    private function requiresLogin($response) {
        if (!$response) return true;
        
        $loginPatterns = ['login', '登录', 'signin', '请先登录'];
        foreach ($loginPatterns as $pattern) {
            if (stripos($response, $pattern) !== false) {
                return true;
            }
        }
        return false;
    }
    
    private function requiresAdmin($response) {
        if (!$response) return true;
        
        $adminPatterns = ['admin', '管理员', 'permission', '权限', 'access denied', '拒绝访问'];
        foreach ($adminPatterns as $pattern) {
            if (stripos($response, $pattern) !== false) {
                return true;
            }
        }
        return false;
    }
    
    private function checkPasswordEncryption() {
        // 检查数据库中的密码是否加密
        try {
            $pdo = new PDO('sqlite:data/boyou_bbs.db');
            $stmt = $pdo->query("SELECT password FROM bbs_user LIMIT 1");
            $password = $stmt->fetchColumn();
            
            // 如果密码长度大于原始密码且包含特殊字符，可能是加密的
            return $password && strlen($password) > 10 && preg_match('/[a-f0-9]{32,}/', $password);
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function addResult($test, $success, $message) {
        $this->testResults[] = [
            'test' => $test,
            'success' => $success,
            'message' => $message
        ];
        
        $status = $success ? '✓' : '✗';
        echo "  $status $test: $message\n";
    }
    
    private function generateReport() {
        echo "\n=== 用户功能测试报告 ===\n";
        
        $total = count($this->testResults);
        $passed = array_filter($this->testResults, function($result) {
            return $result['success'];
        });
        $passedCount = count($passed);
        $failedCount = $total - $passedCount;
        
        echo "总测试数: $total\n";
        echo "通过测试: $passedCount\n";
        echo "失败测试: $failedCount\n";
        echo "成功率: " . round(($passedCount / $total) * 100, 1) . "%\n";
        
        if ($failedCount > 0) {
            echo "\n失败的测试:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "  ✗ {$result['test']}: {$result['message']}\n";
                }
            }
        }
        
        // 保存测试报告
        $reportData = [
            'test_time' => date('Y-m-d H:i:s'),
            'test_user' => $this->testUser['username'],
            'total_tests' => $total,
            'passed_tests' => $passedCount,
            'failed_tests' => $failedCount,
            'success_rate' => round(($passedCount / $total) * 100, 1),
            'results' => $this->testResults
        ];
        
        file_put_contents('user_functionality_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n详细报告已保存到: user_functionality_report.json\n";
        
        if ($passedCount === $total) {
            echo "\n🎉 所有用户功能测试都通过了！\n";
        } elseif ($passedCount / $total >= 0.8) {
            echo "\n⚠️  大部分用户功能正常，有少量问题\n";
        } else {
            echo "\n❌ 用户功能有较多问题，需要检查\n";
        }
    }
}

// 主程序
$tester = new UserFunctionalityTester();
$tester->runAllTests();

echo "\n=== 用户功能测试完成 ===\n";

?>
