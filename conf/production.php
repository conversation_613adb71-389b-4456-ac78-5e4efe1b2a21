<?php

// Boyou BBS 6.1 生产环境配置
// 生成于 2025-07-05

return array (
	// 数据库配置
	'db' => array (
		'type' => 'pdo_sqlite',
		'pdo_sqlite' => array (
			'master' => array (
				'host' => './data/boyou_bbs.db',
				'user' => '',
				'password' => '',
				'name' => '',
				'charset' => '',
				'engine' => '',
				'tablepre' => 'bbs_',
			),
			'slaves' => array (),
		),
	),
	
	// 缓存配置
	'cache' => array (
		'enable' => true,
		'type' => 'mysql',
		'mysql' => array (
			'cachepre' => 'bbs_',
		),
	),
	
	// 路径配置
	'tmp_path' => './tmp/',
	'log_path' => './log/',
	'upload_path' => './upload/',
	'view_url' => 'view/',
	'upload_url' => 'upload/',
	
	// Logo 配置
	'logo_mobile_url' => 'view/img/logo.png',
	'logo_pc_url' => 'view/img/logo.png',
	'logo_water_url' => 'view/img/water-small.png',
	
	// 站点信息
	'sitename' => 'Boyou BBS',
	'sitebrief' => 'Boyou BBS 6.1 - 现代化的论坛系统',
	'timezone' => 'Asia/Shanghai',
	'lang' => 'zh-cn',
	
	// 运行级别 (生产环境)
	'runlevel' => 5,
	'runlevel_reason' => 'The site is under maintenance, please visit later.',
	
	// Cookie 配置
	'cookie_domain' => '',
	'cookie_path' => '/',
	'auth_key' => 'boyou_bbs_6.1_production_' . md5('production_key_' . time()),
	
	// 分页配置
	'pagesize' => 20,
	'postlist_pagesize' => 100,
	'cache_thread_list_pages' => 10,
	
	// 在线用户配置
	'online_update_span' => 120,
	'online_hold_time' => 3600,
	'session_delay_update' => 0,
	
	// 上传配置
	'upload_image_width' => 927,
	'attach_dir_save_rule' => 'Ym',
	
	// 默认排序
	'order_default' => 'lastpid',
	
	// 功能开关
	'update_views_on' => 1,
	'user_create_email_on' => 0,
	'user_create_on' => 1,
	'user_resetpw_on' => 1,
	'user_create_group' => 2,
	
	// 安全配置 (生产环境)
	'admin_bind_ip' => 0,
	'cdn_on' => 0,
	'url_rewrite_on' => 0,
	'disabled_plugin' => 0,
	
	// 版本信息
	'version' => '6.1.0',
	'static_version' => '?2.0',
	'installed' => 1,
	
	// 生产环境特定配置
	'production_mode' => true,
	'debug_mode' => false,
	'error_display' => false,
	'error_logging' => true,
	
	// 性能优化
	'enable_opcache' => true,
	'enable_gzip' => true,
	'cache_expire_time' => 3600,
	
	// 安全增强
	'session_secure' => false, // 如果使用 HTTPS 则设为 true
	'csrf_protection' => true,
	'xss_protection' => true,
	'sql_injection_protection' => true,
);
?>
