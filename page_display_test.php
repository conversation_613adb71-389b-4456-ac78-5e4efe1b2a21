<?php

echo "=== Boyou BBS 6.1 页面显示测试 ===\n\n";

class PageDisplayTester {
    private $baseUrl = 'http://localhost:8000';
    private $testResults = [];
    
    public function runAllTests() {
        echo "开始页面显示测试...\n\n";
        
        $this->testMainPages();
        $this->testForumPages();
        $this->testUserPages();
        $this->testAdminPages();
        $this->testSpecialPages();
        $this->testMobileResponsive();
        
        $this->generateReport();
    }
    
    private function testMainPages() {
        echo "1. 测试主要页面...\n";
        
        $pages = [
            '/' => '首页',
            '/index.php' => '首页(index.php)',
            '/forum.php' => '论坛列表',
            '/thread.php' => '主题列表',
            '/post.php' => '帖子页面',
            '/user.php' => '用户页面'
        ];
        
        foreach ($pages as $path => $description) {
            $this->testPageDisplay($path, $description);
        }
    }
    
    private function testForumPages() {
        echo "2. 测试论坛相关页面...\n";
        
        $pages = [
            '/forum.php?fid=1' => '论坛版块页面',
            '/thread.php?fid=1' => '版块主题列表',
            '/post.php?action=newthread&fid=1' => '发新主题页面',
            '/post.php?action=reply&tid=1' => '回复主题页面',
            '/search.php' => '搜索页面'
        ];
        
        foreach ($pages as $path => $description) {
            $this->testPageDisplay($path, $description);
        }
    }
    
    private function testUserPages() {
        echo "3. 测试用户相关页面...\n";
        
        $pages = [
            '/user.php?action=login' => '用户登录页面',
            '/user.php?action=register' => '用户注册页面',
            '/user.php?action=profile' => '用户资料页面',
            '/user.php?action=setting' => '用户设置页面',
            '/user.php?uid=1' => '用户信息页面',
            '/pm.php' => '私信页面'
        ];
        
        foreach ($pages as $path => $description) {
            $this->testPageDisplay($path, $description);
        }
    }
    
    private function testAdminPages() {
        echo "4. 测试管理后台页面...\n";
        
        $pages = [
            '/admin/' => '管理后台首页',
            '/admin/index.php' => '管理后台主页',
            '/admin/user.php' => '用户管理',
            '/admin/forum.php' => '版块管理',
            '/admin/thread.php' => '主题管理',
            '/admin/setting.php' => '系统设置'
        ];
        
        foreach ($pages as $path => $description) {
            $this->testPageDisplay($path, $description);
        }
    }
    
    private function testSpecialPages() {
        echo "5. 测试特殊页面...\n";
        
        $pages = [
            '/misc.php?action=online' => '在线用户',
            '/misc.php?action=stats' => '统计信息',
            '/misc.php?action=help' => '帮助页面',
            '/misc.php?action=about' => '关于页面',
            '/rss.php' => 'RSS订阅',
            '/sitemap.xml' => '网站地图'
        ];
        
        foreach ($pages as $path => $description) {
            $this->testPageDisplay($path, $description);
        }
    }
    
    private function testMobileResponsive() {
        echo "6. 测试移动端响应式...\n";
        
        $mobileUserAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15';
        
        $pages = [
            '/' => '移动端首页',
            '/forum.php' => '移动端论坛',
            '/user.php?action=login' => '移动端登录'
        ];
        
        foreach ($pages as $path => $description) {
            $this->testPageDisplay($path, $description, $mobileUserAgent);
        }
    }
    
    private function testPageDisplay($path, $description, $userAgent = null) {
        $url = $this->baseUrl . $path;
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET',
                'header' => $userAgent ? "User-Agent: $userAgent\r\n" : ''
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $this->addResult($description, false, "页面无法访问");
            return;
        }
        
        // 检查基本HTML结构
        $checks = [
            'html_structure' => $this->checkHtmlStructure($response),
            'charset' => $this->checkCharset($response),
            'title' => $this->checkTitle($response),
            'css_links' => $this->checkCssLinks($response),
            'js_scripts' => $this->checkJsScripts($response),
            'content_length' => $this->checkContentLength($response),
            'error_messages' => $this->checkErrorMessages($response),
            'boyou_branding' => $this->checkBoyouBranding($response)
        ];
        
        $passedChecks = array_filter($checks);
        $totalChecks = count($checks);
        $passedCount = count($passedChecks);
        
        if ($passedCount === $totalChecks) {
            $this->addResult($description, true, "页面完全正常 ($passedCount/$totalChecks)");
        } elseif ($passedCount >= $totalChecks * 0.7) {
            $failedChecks = array_keys(array_filter($checks, function($v) { return !$v; }));
            $this->addResult($description, true, "页面基本正常 ($passedCount/$totalChecks) - 问题: " . implode(', ', $failedChecks));
        } else {
            $failedChecks = array_keys(array_filter($checks, function($v) { return !$v; }));
            $this->addResult($description, false, "页面有问题 ($passedCount/$totalChecks) - 失败: " . implode(', ', $failedChecks));
        }
    }
    
    private function checkHtmlStructure($content) {
        return strpos($content, '<html') !== false && 
               strpos($content, '<head>') !== false && 
               strpos($content, '<body>') !== false && 
               strpos($content, '</html>') !== false;
    }
    
    private function checkCharset($content) {
        return strpos($content, 'charset=utf-8') !== false || 
               strpos($content, 'charset="utf-8"') !== false;
    }
    
    private function checkTitle($content) {
        return preg_match('/<title[^>]*>.*?<\/title>/i', $content);
    }
    
    private function checkCssLinks($content) {
        return strpos($content, 'bootstrap.css') !== false || 
               strpos($content, '.css') !== false;
    }
    
    private function checkJsScripts($content) {
        return strpos($content, 'jquery') !== false || 
               strpos($content, '.js') !== false;
    }
    
    private function checkContentLength($content) {
        return strlen($content) > 500; // 至少500字符
    }
    
    private function checkErrorMessages($content) {
        $errorPatterns = [
            'Fatal error',
            'Parse error',
            'Warning:',
            'Notice:',
            'Undefined',
            'Call to undefined',
            'Cannot redeclare'
        ];
        
        foreach ($errorPatterns as $pattern) {
            if (stripos($content, $pattern) !== false) {
                return false;
            }
        }
        return true;
    }
    
    private function checkBoyouBranding($content) {
        return stripos($content, 'boyou') !== false || 
               stripos($content, 'bbs') !== false;
    }
    
    private function addResult($test, $success, $message) {
        $this->testResults[] = [
            'test' => $test,
            'success' => $success,
            'message' => $message
        ];
        
        $status = $success ? '✓' : '✗';
        echo "  $status $test: $message\n";
    }
    
    private function generateReport() {
        echo "\n=== 页面显示测试报告 ===\n";
        
        $total = count($this->testResults);
        $passed = array_filter($this->testResults, function($result) {
            return $result['success'];
        });
        $passedCount = count($passed);
        $failedCount = $total - $passedCount;
        
        echo "总页面数: $total\n";
        echo "正常页面: $passedCount\n";
        echo "异常页面: $failedCount\n";
        echo "成功率: " . round(($passedCount / $total) * 100, 1) . "%\n";
        
        if ($failedCount > 0) {
            echo "\n异常页面详情:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "  ✗ {$result['test']}: {$result['message']}\n";
                }
            }
        }
        
        // 保存详细报告
        $reportData = [
            'test_time' => date('Y-m-d H:i:s'),
            'total_pages' => $total,
            'passed_pages' => $passedCount,
            'failed_pages' => $failedCount,
            'success_rate' => round(($passedCount / $total) * 100, 1),
            'results' => $this->testResults
        ];
        
        file_put_contents('page_display_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n详细报告已保存到: page_display_report.json\n";
        
        if ($passedCount === $total) {
            echo "\n🎉 所有页面都正常显示！\n";
        } elseif ($passedCount / $total >= 0.8) {
            echo "\n⚠️  大部分页面正常，有少量问题\n";
        } else {
            echo "\n❌ 多个页面有显示问题，需要检查\n";
        }
    }
}

// 主程序
$tester = new PageDisplayTester();
$tester->runAllTests();

echo "\n=== 页面显示测试完成 ===\n";

?>
