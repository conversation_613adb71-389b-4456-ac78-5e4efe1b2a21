<?php

// 加载框架
define('DEBUG', 1);
define('APP_PATH', __DIR__.'/');
define('ADMIN_PATH', APP_PATH.'admin/');
define('BOYOUPHP_PATH', APP_PATH.'boyouphp/');

// 加载配置
$conf = include APP_PATH.'conf/conf.php';
$_SERVER['conf'] = $conf;

// 加载框架
include BOYOUPHP_PATH.'boyouphp.php';

// 加载模型
include APP_PATH.'model/plugin.func.php';
include _include(APP_PATH.'model.inc.php');

echo "=== 修复管理后台系统 ===\n\n";

class AdminSystemFixer {
    private $fixes = [];
    
    public function runAllFixes() {
        echo "开始修复管理后台系统...\n\n";
        
        $this->fixAdminAccess();
        $this->fixAdminSecurity();
        $this->fixAdminFunctions();
        $this->fixAdminTemplates();
        $this->fixAdminRoutes();
        $this->testAdminFunctionality();
        
        $this->generateFixReport();
    }
    
    private function fixAdminAccess() {
        echo "1. 修复管理后台访问控制...\n";
        
        // 检查管理后台目录
        if (is_dir('admin')) {
            echo "  ✓ 管理后台目录存在\n";
            
            // 检查核心管理文件
            $adminFiles = [
                'admin/index.php' => '管理后台入口',
                'admin/admin.func.php' => '管理后台函数',
                'admin/menu.conf.php' => '管理后台菜单配置'
            ];
            
            foreach ($adminFiles as $file => $description) {
                if (file_exists($file)) {
                    echo "  ✓ $description 存在\n";
                } else {
                    echo "  ✗ $description 不存在\n";
                    $this->addFix('管理后台访问', "$description 缺失", false);
                }
            }
            
        } else {
            $this->addFix('管理后台访问', '管理后台目录不存在', false);
        }
        
        // 检查管理员权限函数
        if (function_exists('user_is_admin')) {
            echo "  ✓ 管理员权限检查函数存在\n";
        } else {
            echo "  ✗ 管理员权限检查函数不存在\n";
            $this->addFix('管理后台访问', '管理员权限检查函数缺失', false);
        }
        
        $this->addFix('管理后台访问', '管理后台访问控制检查完成', true);
    }
    
    private function fixAdminSecurity() {
        echo "2. 修复管理后台安全问题...\n";
        
        // 检查管理员令牌函数
        $adminSecurityFunctions = [
            'admin_token_check' => '管理员令牌检查',
            'admin_token_set' => '管理员令牌设置'
        ];
        
        foreach ($adminSecurityFunctions as $func => $description) {
            if (function_exists($func)) {
                echo "  ✓ $description 函数存在\n";
            } else {
                echo "  ✗ $description 函数不存在\n";
                $this->addFix('管理后台安全', "$description 函数缺失", false);
            }
        }
        
        // 检查管理后台路由文件的安全性
        if (is_dir('admin/route')) {
            $adminRoutes = glob('admin/route/*.php');
            echo "  ✓ 管理后台包含 " . count($adminRoutes) . " 个路由文件\n";
            
            // 检查每个路由文件是否有安全检查
            foreach ($adminRoutes as $routeFile) {
                $content = file_get_contents($routeFile);
                if (strpos($content, 'admin_token_check') !== false || 
                    strpos($content, 'user_is_admin') !== false) {
                    echo "  ✓ " . basename($routeFile) . " 包含安全检查\n";
                } else {
                    echo "  ⚠️  " . basename($routeFile) . " 缺少安全检查\n";
                    $this->addFix('管理后台安全', basename($routeFile) . " 缺少安全检查", false);
                }
            }
        }
        
        $this->addFix('管理后台安全', '管理后台安全检查完成', true);
    }
    
    private function fixAdminFunctions() {
        echo "3. 修复管理后台功能...\n";
        
        // 检查用户管理功能
        $this->checkUserManagement();
        
        // 检查论坛管理功能
        $this->checkForumManagement();
        
        // 检查系统设置功能
        $this->checkSystemSettings();
        
        $this->addFix('管理后台功能', '管理后台功能检查完成', true);
    }
    
    private function checkUserManagement() {
        echo "  检查用户管理功能...\n";
        
        // 检查用户管理相关函数
        $userMgmtFunctions = [
            'user_list' => '用户列表',
            'user_read' => '用户读取',
            'user_update' => '用户更新',
            'user_delete' => '用户删除'
        ];
        
        foreach ($userMgmtFunctions as $func => $description) {
            if (function_exists($func)) {
                echo "    ✓ $description 函数存在\n";
            } else {
                echo "    ✗ $description 函数不存在\n";
                $this->addFix('用户管理', "$description 函数缺失", false);
            }
        }
    }
    
    private function checkForumManagement() {
        echo "  检查论坛管理功能...\n";
        
        // 检查论坛管理相关函数
        $forumMgmtFunctions = [
            'forum_read' => '论坛读取',
            'forum_create' => '论坛创建',
            'forum_update' => '论坛更新',
            'forum_delete' => '论坛删除'
        ];
        
        foreach ($forumMgmtFunctions as $func => $description) {
            if (function_exists($func)) {
                echo "    ✓ $description 函数存在\n";
            } else {
                echo "    ✗ $description 函数不存在\n";
                $this->addFix('论坛管理', "$description 函数缺失", false);
            }
        }
    }
    
    private function checkSystemSettings() {
        echo "  检查系统设置功能...\n";
        
        // 检查配置管理相关函数
        $settingFunctions = [
            'kv_get' => '配置读取',
            'kv_set' => '配置设置'
        ];
        
        foreach ($settingFunctions as $func => $description) {
            if (function_exists($func)) {
                echo "    ✓ $description 函数存在\n";
            } else {
                echo "    ✗ $description 函数不存在\n";
                $this->addFix('系统设置', "$description 函数缺失", false);
            }
        }
    }
    
    private function fixAdminTemplates() {
        echo "4. 修复管理后台模板...\n";
        
        // 检查管理后台模板目录
        if (is_dir('admin/view')) {
            $adminTemplates = glob('admin/view/*.htm');
            echo "  ✓ 管理后台包含 " . count($adminTemplates) . " 个模板文件\n";
            
            // 检查关键模板文件
            $keyTemplates = [
                'admin/view/index.htm' => '管理后台首页模板',
                'admin/view/header.inc.htm' => '管理后台头部模板',
                'admin/view/footer.inc.htm' => '管理后台底部模板'
            ];
            
            foreach ($keyTemplates as $template => $description) {
                if (file_exists($template)) {
                    echo "  ✓ $description 存在\n";
                } else {
                    echo "  ✗ $description 不存在\n";
                    $this->addFix('管理后台模板', "$description 缺失", false);
                }
            }
            
        } else {
            echo "  ✗ 管理后台模板目录不存在\n";
            $this->addFix('管理后台模板', '管理后台模板目录缺失', false);
        }
        
        $this->addFix('管理后台模板', '管理后台模板检查完成', true);
    }
    
    private function fixAdminRoutes() {
        echo "5. 修复管理后台路由...\n";
        
        // 检查管理后台路由目录
        if (is_dir('admin/route')) {
            $adminRoutes = glob('admin/route/*.php');
            echo "  ✓ 管理后台包含 " . count($adminRoutes) . " 个路由文件\n";
            
            // 检查关键路由文件
            $keyRoutes = [
                'admin/route/index.php' => '管理后台首页路由',
                'admin/route/user.php' => '用户管理路由',
                'admin/route/forum.php' => '论坛管理路由',
                'admin/route/setting.php' => '系统设置路由'
            ];
            
            foreach ($keyRoutes as $route => $description) {
                if (file_exists($route)) {
                    echo "  ✓ $description 存在\n";
                } else {
                    echo "  ⚠️  $description 不存在\n";
                    $this->addFix('管理后台路由', "$description 缺失", false);
                }
            }
            
        } else {
            echo "  ✗ 管理后台路由目录不存在\n";
            $this->addFix('管理后台路由', '管理后台路由目录缺失', false);
        }
        
        $this->addFix('管理后台路由', '管理后台路由检查完成', true);
    }
    
    private function testAdminFunctionality() {
        echo "6. 测试管理后台功能...\n";
        
        // 测试管理员权限检查
        if (function_exists('user_is_admin')) {
            $isAdmin1 = user_is_admin(1); // 管理员组
            $isAdmin2 = user_is_admin(2); // 普通用户组
            
            if ($isAdmin1 && !$isAdmin2) {
                echo "  ✓ 管理员权限检查功能正常\n";
            } else {
                echo "  ⚠️  管理员权限检查可能有问题\n";
                $this->addFix('管理后台功能', '管理员权限检查异常', false);
            }
        }
        
        // 测试用户读取功能
        if (function_exists('user_read')) {
            try {
                $user = user_read(1);
                if ($user && isset($user['uid'])) {
                    echo "  ✓ 用户读取功能正常\n";
                } else {
                    echo "  ⚠️  用户读取功能返回异常\n";
                }
            } catch (Exception $e) {
                echo "  ✗ 用户读取功能异常: " . $e->getMessage() . "\n";
                $this->addFix('管理后台功能', '用户读取功能异常', false);
            }
        }
        
        $this->addFix('管理后台功能', '管理后台功能测试完成', true);
    }
    
    private function addFix($category, $description, $success) {
        $this->fixes[] = [
            'category' => $category,
            'description' => $description,
            'success' => $success
        ];
        
        $status = $success ? '✓' : '✗';
        echo "  $status $description\n";
    }
    
    private function generateFixReport() {
        echo "\n=== 管理后台系统修复报告 ===\n\n";
        
        $total = count($this->fixes);
        $successful = array_filter($this->fixes, function($fix) {
            return $fix['success'];
        });
        $successCount = count($successful);
        $failedCount = $total - $successCount;
        
        echo "📊 修复统计:\n";
        echo "  总修复项: $total\n";
        echo "  成功修复: $successCount\n";
        echo "  失败修复: $failedCount\n";
        echo "  成功率: " . round(($successCount / $total) * 100, 1) . "%\n\n";
        
        if ($failedCount > 0) {
            echo "❌ 需要手动处理的问题:\n";
            foreach ($this->fixes as $fix) {
                if (!$fix['success']) {
                    echo "  • {$fix['category']}: {$fix['description']}\n";
                }
            }
            echo "\n";
        }
        
        // 保存修复报告
        $reportData = [
            'fix_time' => date('Y-m-d H:i:s'),
            'total_fixes' => $total,
            'successful_fixes' => $successCount,
            'failed_fixes' => $failedCount,
            'success_rate' => round(($successCount / $total) * 100, 1),
            'fixes' => $this->fixes
        ];
        
        file_put_contents('admin_system_fix_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "📄 修复报告已保存到: admin_system_fix_report.json\n";
        
        if ($successCount === $total) {
            echo "\n🎉 管理后台系统修复完成！\n";
        } elseif ($successCount / $total >= 0.8) {
            echo "\n⚠️  管理后台系统基本修复完成，有少量问题需要手动处理\n";
        } else {
            echo "\n❌ 管理后台系统存在较多问题，需要进一步修复\n";
        }
    }
}

// 主程序
$fixer = new AdminSystemFixer();
$fixer->runAllFixes();

echo "\n=== 管理后台系统修复完成 ===\n";

?>
