<?php
/* Thai translation by jaid<PERSON><PERSON>g007, https://xiunothai.ml/ */
return array(
	'installed_tips' => 'คุณได้ติดตั้งไปแล้ว หากต้องการติดตั้งใหม่ กรุณาลบไฟล์ conf/conf.php ออกก่อน',
	'please_set_conf_file_writable' => 'กรุณากำหนดสิทธิ์ไฟล์ให้สามารถเขียนไฟล์ conf/conf.php ได้',
	'evn_not_support_php_mysql' => 'สภาพแวดล้อม PHP ที่ใช้ในปัจจุบัน ยังไม่รองรับ mysql และ pdo_mysql การติดตั้งจะไม่สามารถดำเนินการต่อได้',
	'dbhost_is_empty' => 'โฮสต์ฐานข้อมูลจะต้องไม่ว่างเปล่า',
	'dbname_is_empty' => 'ชื่อฐานข้อมูลจะต้องไม่ว่างเปล่า',
	'dbuser_is_empty' => 'ชื่อผู้ใช้ฐานข้อมูลจะต้องไม่ว่างเปล่า',
	'adminuser_is_empty' => 'ชื่อแอดมินจะต้องไม่ว่างเปล่า',
	'adminpass_is_empty' => 'รหัสผ่านแอดมินจะต้องไม่ว่างเปล่า',
	'conguralation_installed' => 'ยินดีด้วยจ้า ติดตั้งเสร็จแล้ว เพื่อความปลอดภัย กรุณาลบโฟลเดอร์ install ออกก่อนใช้งาน',
	
	'step_1_title' => '1. ตรวจสอบสภาพแวดล้อม',
	'runtime_env_check' => 'ตรวจสอบสภาพแวดล้อมรันไทม์',
	'required' => 'ต้องการ',
	'current' => 'ปัจจุบัน',
	'check_result' => 'ผลการตรวจ',
	'passed' => 'ผ่าน',
	'not_passed' => 'ไม่ผ่าน',
	'not_the_best' => 'สภาพแวดล้อมไม่เหมาะสม',
	'dir_writable_check' => 'สิทธิการเข้าถึงไฟล์และโฟลเดอร์',
	'writable' => 'เขียนได้',
	'unwritable' => 'เขียนไม่ได้',
	'check_again' => 'ตรวจสอบอีกครั้ง',
	'os' => 'OS',
	'unix_like' => 'UNIX Like',
	'php_version' => 'เวอร์ชัน PHP',
	
	'step_2_title' => '2. ตั้งค่าฐานข้อมูล',
	'db_type' => 'ประเภทฐานข้อมูล',
	'db_engine' => 'โปรแกรมฐานข้อมูล',
	'db_host' => 'โฮสต์ฐานข้อมูล',
	'db_name' => 'ชื่อฐานข้อมูล',
	'db_user' => 'ชื่อผู้ใช้ฐานข้อมูล',
	'db_pass' => 'รหัสผ่านฐานข้อมูล',
	'step_3_title' => '3. ข้อมูลแอดมิน',
	'admin_email' => 'อีเมลแอดมิน',
	'admin_username' => 'ชื่อผู้ใช้แอดมิน',
	'admin_pw' => 'รหัสผ่านแอดมิน',
	'installing_about_moment' => 'กำลังติดตั้ง ใช้เวลาประมาณ 1 นาทีหรือมากกว่านั้น',
	'license_title' => 'ข้อตกลง Boyou BBS 4.0',
	'license_content' => 'ขอขอบคุณที่เลือกใช้งาน BBS Xiuno 4  ซึ่งเป็นระบบเว็บบอร์ดที่มีขนาดกะทัดรัด เสถียร และยังรองรับความสามารถในการโหลดข้อมูลปริมาณสูงได้ ตัวระบบฐานข้อมูลมีเพียง 20 กว่าตาราง ซอร์สโค้ดบีบอัดแล้วมีขนาดไม่เกิน 1M การประมวลผลหนึ่งคำขอได้ใน 0.01 วินาที มีระบบไฟล์แคช APC, Xcache, Yac สามารถเรียกการทำงานได้ไวถึง 0.00x วินาที ไลบราลีจากบุคคลที่สามมีการอ้างอิงเป็นส่วนน้อย โดยหลัก ๆ มีเพียง jquery.js ซึ่งเหมือนกับรถที่ทำขึ้นอย่างเฟอร์รารีที่ทรงพลัง โดยไม่ต้องทำอะไรเพิ่มเติมแม้แต่น้อย ใช้งานสะดวกสบาย  และการบำรุงรักษาก็เป็นรากฐานที่สำคัญของการพัฒนาในระดับรองที่ดีมาก
Boyou BBS 4.0 ใช้งาน Bootstrap 4 + jQuery 3 เป็นไลบราลีระบบหน้าบ้าน รองรับเบราว์เซอร์มือถือเต็มรูปแบบ และระบบหลังบ้านมีการใช้งาน XiunoPHP 4.0 รองรับ NoSQL เป็นช่องทางการทำงานกับฐานข้อมูลที่หลากหลาย เวอร์ชันนี้เป็นก้าวใหญ่ที่สำคัญของเรา
Xiuno ออกเสียงว่า "ชูร่า" (Shura) มาจากอาชูร่า (หรือที่เรียกกันว่าอสุรา) ในศาสนาพุทธเป็นหนึ่งในหก "เซียน" ที่อยู่ระหว่างมนุษย์และสวรรค์
Boyou BBS 4 ใช้งานข้อตกลง MIT คุณสามารถแก้ไขได้อย่างอิสระ เปลี่ยนแปลงเวอร์ชัน หรือใช้งานในเชิงพาณิชย์ได้โดยไม่ต้องกังวลในทางกฎหมายใด ๆ (ควรเก็บข้อมูลลิขสิทธิ์เดิมไว้)
	',
	'license_date' => 'วันที่ปล่อย: 22 มกราคม 2018',
	'agree_license_to_continue' => 'ยอมรับข้อตกลงและดำเนินการติดตั้งต่อ',
	'install_title' => 'ตัวช่วยติดตั้ง Boyou BBS 4.0',
	'install_guide' => 'ตัวช่วยติดตั้ง',

	
	'function_check' => 'ตรวจสอบฟังก์ชันที่ต้องการ',
	'supported' => 'รองรับ',
	'not_supported' => 'ไม่รองรับ',
	'function_glob_not_exists' => 'จำเป็นต้องมีการติดตั้งส่วนขยายเพิ่มเติม กรุณาตั้งค่าในไฟล์ php.ini ให้ disabled_functions = ; ยกเว้นข้อจำกัดในฟังก์ชั่นนี้',
	'function_gzcompress_not_exists' => 'จำเป็นต้องมีการติดตั้งส่วนขยายเพิ่มเติม สำหรับเซิร์ฟเวอร์ Linux ให้เพิ่ม compile argument: --with-zlib, ส่วนเซิร์ฟเวอร์ Windows กรุณาตั้งค่า php.ini ให้เปิด extension=php_zlib.dll',
	'function_mb_substr_not_exists' => 'จำเป็นต้องมีในระบบ สำหรับเซิร์ฟเวอร์ Linux ให้เพิ่ม compile argument: --with-mbstring, ส่วนเซิร์ฟเวอร์ Windows กรุณาตั้งค่า php.ini ให้เปิด extension=php_mbstring.dll',
	
	// hook lang_th_th_bbs_install.php
);

?>