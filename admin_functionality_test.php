<?php

echo "=== Boyou BBS 6.1 管理后台功能测试 ===\n\n";

class AdminFunctionalityTester {
    private $baseUrl = 'http://localhost:8000';
    private $testResults = [];
    
    public function runAllTests() {
        echo "开始管理后台功能测试...\n\n";
        
        $this->testAdminAccess();
        $this->testAdminPages();
        $this->testUserManagement();
        $this->testForumManagement();
        $this->testContentManagement();
        $this->testSystemSettings();
        $this->testSecurityFeatures();
        $this->testDataManagement();
        
        $this->generateReport();
    }
    
    private function testAdminAccess() {
        echo "1. 测试管理后台访问...\n";
        
        // 测试管理后台首页
        $adminIndex = $this->makeRequest('/admin/');
        if ($adminIndex && strpos($adminIndex, 'admin') !== false) {
            $this->addResult('管理后台首页', true, '管理后台首页可访问');
        } else {
            $this->addResult('管理后台首页', false, '管理后台首页无法访问');
        }
        
        // 测试管理后台主页
        $adminMain = $this->makeRequest('/admin/index.php');
        if ($adminMain && strpos($adminMain, 'admin') !== false) {
            $this->addResult('管理后台主页', true, '管理后台主页正常');
        } else {
            $this->addResult('管理后台主页', false, '管理后台主页异常');
        }
        
        // 检查管理后台布局
        if ($adminIndex && $this->checkAdminLayout($adminIndex)) {
            $this->addResult('管理后台布局', true, '管理后台布局完整');
        } else {
            $this->addResult('管理后台布局', false, '管理后台布局不完整');
        }
    }
    
    private function testAdminPages() {
        echo "2. 测试管理后台页面...\n";
        
        $adminPages = [
            '/admin/user.php' => '用户管理页面',
            '/admin/forum.php' => '版块管理页面',
            '/admin/thread.php' => '主题管理页面',
            '/admin/post.php' => '帖子管理页面',
            '/admin/setting.php' => '系统设置页面',
            '/admin/plugin.php' => '插件管理页面',
            '/admin/cache.php' => '缓存管理页面',
            '/admin/backup.php' => '备份管理页面'
        ];
        
        foreach ($adminPages as $path => $description) {
            $response = $this->makeRequest($path);
            if ($response && $this->isValidAdminPage($response)) {
                $this->addResult($description, true, '页面正常加载');
            } else {
                $this->addResult($description, false, '页面加载失败或异常');
            }
        }
    }
    
    private function testUserManagement() {
        echo "3. 测试用户管理功能...\n";
        
        // 测试用户列表
        $userList = $this->makeRequest('/admin/user.php');
        if ($userList && strpos($userList, 'user') !== false) {
            $this->addResult('用户列表显示', true, '用户列表正常显示');
        } else {
            $this->addResult('用户列表显示', false, '用户列表显示异常');
        }
        
        // 测试用户搜索
        $userSearch = $this->makeRequest('/admin/user.php?action=search&keyword=admin');
        if ($userSearch) {
            $this->addResult('用户搜索功能', true, '用户搜索功能可用');
        } else {
            $this->addResult('用户搜索功能', false, '用户搜索功能异常');
        }
        
        // 测试用户编辑页面
        $userEdit = $this->makeRequest('/admin/user.php?action=edit&uid=1');
        if ($userEdit && strpos($userEdit, 'edit') !== false) {
            $this->addResult('用户编辑页面', true, '用户编辑页面正常');
        } else {
            $this->addResult('用户编辑页面', false, '用户编辑页面异常');
        }
    }
    
    private function testForumManagement() {
        echo "4. 测试版块管理功能...\n";
        
        // 测试版块列表
        $forumList = $this->makeRequest('/admin/forum.php');
        if ($forumList && strpos($forumList, 'forum') !== false) {
            $this->addResult('版块列表显示', true, '版块列表正常显示');
        } else {
            $this->addResult('版块列表显示', false, '版块列表显示异常');
        }
        
        // 测试版块创建页面
        $forumCreate = $this->makeRequest('/admin/forum.php?action=create');
        if ($forumCreate && strpos($forumCreate, 'create') !== false) {
            $this->addResult('版块创建页面', true, '版块创建页面正常');
        } else {
            $this->addResult('版块创建页面', false, '版块创建页面异常');
        }
        
        // 测试版块编辑页面
        $forumEdit = $this->makeRequest('/admin/forum.php?action=edit&fid=1');
        if ($forumEdit && strpos($forumEdit, 'edit') !== false) {
            $this->addResult('版块编辑页面', true, '版块编辑页面正常');
        } else {
            $this->addResult('版块编辑页面', false, '版块编辑页面异常');
        }
    }
    
    private function testContentManagement() {
        echo "5. 测试内容管理功能...\n";
        
        // 测试主题管理
        $threadManage = $this->makeRequest('/admin/thread.php');
        if ($threadManage && strpos($threadManage, 'thread') !== false) {
            $this->addResult('主题管理页面', true, '主题管理页面正常');
        } else {
            $this->addResult('主题管理页面', false, '主题管理页面异常');
        }
        
        // 测试帖子管理
        $postManage = $this->makeRequest('/admin/post.php');
        if ($postManage && strpos($postManage, 'post') !== false) {
            $this->addResult('帖子管理页面', true, '帖子管理页面正常');
        } else {
            $this->addResult('帖子管理页面', false, '帖子管理页面异常');
        }
        
        // 测试内容审核
        $contentReview = $this->makeRequest('/admin/thread.php?action=review');
        if ($contentReview) {
            $this->addResult('内容审核功能', true, '内容审核功能可用');
        } else {
            $this->addResult('内容审核功能', false, '内容审核功能异常');
        }
    }
    
    private function testSystemSettings() {
        echo "6. 测试系统设置功能...\n";
        
        // 测试基本设置
        $basicSettings = $this->makeRequest('/admin/setting.php');
        if ($basicSettings && strpos($basicSettings, 'setting') !== false) {
            $this->addResult('基本设置页面', true, '基本设置页面正常');
        } else {
            $this->addResult('基本设置页面', false, '基本设置页面异常');
        }
        
        // 测试邮件设置
        $mailSettings = $this->makeRequest('/admin/setting.php?action=mail');
        if ($mailSettings) {
            $this->addResult('邮件设置页面', true, '邮件设置页面可用');
        } else {
            $this->addResult('邮件设置页面', false, '邮件设置页面异常');
        }
        
        // 测试SEO设置
        $seoSettings = $this->makeRequest('/admin/setting.php?action=seo');
        if ($seoSettings) {
            $this->addResult('SEO设置页面', true, 'SEO设置页面可用');
        } else {
            $this->addResult('SEO设置页面', false, 'SEO设置页面异常');
        }
    }
    
    private function testSecurityFeatures() {
        echo "7. 测试安全功能...\n";
        
        // 测试权限验证
        $guestAccess = $this->makeRequestAsGuest('/admin/');
        if ($this->requiresAuth($guestAccess)) {
            $this->addResult('游客访问控制', true, '游客无法访问管理后台');
        } else {
            $this->addResult('游客访问控制', false, '游客访问控制失败');
        }
        
        // 测试CSRF保护
        $csrfProtection = $this->checkCSRFProtection();
        if ($csrfProtection) {
            $this->addResult('CSRF保护', true, 'CSRF保护机制存在');
        } else {
            $this->addResult('CSRF保护', false, 'CSRF保护机制缺失');
        }
        
        // 测试日志记录
        $logFeature = $this->makeRequest('/admin/log.php');
        if ($logFeature) {
            $this->addResult('日志记录功能', true, '日志记录功能可用');
        } else {
            $this->addResult('日志记录功能', false, '日志记录功能不可用');
        }
    }
    
    private function testDataManagement() {
        echo "8. 测试数据管理功能...\n";
        
        // 测试数据库管理
        $dbManage = $this->makeRequest('/admin/database.php');
        if ($dbManage) {
            $this->addResult('数据库管理', true, '数据库管理功能可用');
        } else {
            $this->addResult('数据库管理', false, '数据库管理功能不可用');
        }
        
        // 测试缓存管理
        $cacheManage = $this->makeRequest('/admin/cache.php');
        if ($cacheManage && strpos($cacheManage, 'cache') !== false) {
            $this->addResult('缓存管理', true, '缓存管理功能正常');
        } else {
            $this->addResult('缓存管理', false, '缓存管理功能异常');
        }
        
        // 测试备份功能
        $backupFeature = $this->makeRequest('/admin/backup.php');
        if ($backupFeature && strpos($backupFeature, 'backup') !== false) {
            $this->addResult('备份功能', true, '备份功能可用');
        } else {
            $this->addResult('备份功能', false, '备份功能不可用');
        }
        
        // 测试统计信息
        $statsPage = $this->makeRequest('/admin/stats.php');
        if ($statsPage) {
            $this->addResult('统计信息', true, '统计信息功能可用');
        } else {
            $this->addResult('统计信息', false, '统计信息功能不可用');
        }
    }
    
    private function makeRequest($path) {
        $url = $this->baseUrl . $path;
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        return @file_get_contents($url, false, $context);
    }
    
    private function makeRequestAsGuest($path) {
        // 模拟游客访问（无认证信息）
        return $this->makeRequest($path);
    }
    
    private function checkAdminLayout($content) {
        $layoutElements = [
            'navigation', 'nav', 'menu', 'sidebar',
            'header', 'footer', 'content', 'main'
        ];
        
        $foundElements = 0;
        foreach ($layoutElements as $element) {
            if (stripos($content, $element) !== false) {
                $foundElements++;
            }
        }
        
        return $foundElements >= 3; // 至少包含3个布局元素
    }
    
    private function isValidAdminPage($content) {
        if (!$content) return false;
        
        // 检查是否包含管理后台特征
        $adminFeatures = [
            'admin', '管理', 'management', 'control',
            'setting', '设置', 'config', '配置'
        ];
        
        foreach ($adminFeatures as $feature) {
            if (stripos($content, $feature) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    private function requiresAuth($content) {
        if (!$content) return true;
        
        $authPatterns = [
            'login', '登录', 'signin', 'authentication',
            'unauthorized', '未授权', 'access denied', '拒绝访问'
        ];
        
        foreach ($authPatterns as $pattern) {
            if (stripos($content, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    private function checkCSRFProtection() {
        // 检查是否有CSRF token相关的代码
        $adminPage = $this->makeRequest('/admin/setting.php');
        if (!$adminPage) return false;
        
        $csrfPatterns = [
            'csrf', 'token', '_token', 'formhash',
            'security_token', 'authenticity_token'
        ];
        
        foreach ($csrfPatterns as $pattern) {
            if (stripos($adminPage, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    private function addResult($test, $success, $message) {
        $this->testResults[] = [
            'test' => $test,
            'success' => $success,
            'message' => $message
        ];
        
        $status = $success ? '✓' : '✗';
        echo "  $status $test: $message\n";
    }
    
    private function generateReport() {
        echo "\n=== 管理后台功能测试报告 ===\n";
        
        $total = count($this->testResults);
        $passed = array_filter($this->testResults, function($result) {
            return $result['success'];
        });
        $passedCount = count($passed);
        $failedCount = $total - $passedCount;
        
        echo "总测试数: $total\n";
        echo "通过测试: $passedCount\n";
        echo "失败测试: $failedCount\n";
        echo "成功率: " . round(($passedCount / $total) * 100, 1) . "%\n";
        
        if ($failedCount > 0) {
            echo "\n失败的测试:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "  ✗ {$result['test']}: {$result['message']}\n";
                }
            }
        }
        
        // 保存测试报告
        $reportData = [
            'test_time' => date('Y-m-d H:i:s'),
            'total_tests' => $total,
            'passed_tests' => $passedCount,
            'failed_tests' => $failedCount,
            'success_rate' => round(($passedCount / $total) * 100, 1),
            'results' => $this->testResults
        ];
        
        file_put_contents('admin_functionality_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n详细报告已保存到: admin_functionality_report.json\n";
        
        if ($passedCount === $total) {
            echo "\n🎉 所有管理后台功能测试都通过了！\n";
        } elseif ($passedCount / $total >= 0.8) {
            echo "\n⚠️  大部分管理后台功能正常，有少量问题\n";
        } else {
            echo "\n❌ 管理后台功能有较多问题，需要检查\n";
        }
    }
}

// 主程序
$tester = new AdminFunctionalityTester();
$tester->runAllTests();

echo "\n=== 管理后台功能测试完成 ===\n";

?>
