<?php

echo "=== Boyou BBS 6.1 网站功能测试 ===\n\n";

class WebsiteTester {
    private $baseUrl = 'http://localhost:8000';
    private $testResults = [];
    
    public function runAllTests() {
        echo "开始网站功能测试...\n\n";
        
        $this->testBasicConnectivity();
        $this->testFrameworkLoading();
        $this->testStaticResources();
        $this->testPageRendering();
        $this->testDatabaseConnection();
        $this->testConfigurationFiles();
        
        $this->generateReport();
    }
    
    private function testBasicConnectivity() {
        echo "1. 测试基本连接性...\n";
        
        // 测试主页是否可访问
        $response = $this->makeRequest('/');
        if ($response !== false && strpos($response, 'Boyou BBS') !== false) {
            $this->addResult('主页访问', true, '主页正常加载，包含Boyou BBS品牌');
        } else {
            $this->addResult('主页访问', false, '主页加载失败或内容异常');
        }
        
        // 测试HTTP状态码
        $headers = get_headers($this->baseUrl, 1);
        if ($headers && strpos($headers[0], '200') !== false) {
            $this->addResult('HTTP状态', true, 'HTTP 200 OK');
        } else {
            $this->addResult('HTTP状态', false, 'HTTP状态异常: ' . ($headers[0] ?? '无响应'));
        }
    }
    
    private function testFrameworkLoading() {
        echo "2. 测试框架加载...\n";
        
        // 检查BoyouPHP框架是否正常加载
        try {
            if (!defined('DEBUG')) define('DEBUG', 1);
            if (!defined('APP_PATH')) define('APP_PATH', './');
            if (!defined('BOYOUPHP_PATH')) define('BOYOUPHP_PATH', './boyouphp/');
            
            ob_start();
            include_once 'boyouphp/boyouphp.php';
            $output = ob_get_clean();
            
            if (defined('BOYOUPHP_VERSION')) {
                $this->addResult('框架加载', true, 'BoyouPHP ' . BOYOUPHP_VERSION . ' 加载成功');
            } else {
                $this->addResult('框架加载', false, '框架版本常量未定义');
            }
            
            // 测试核心函数
            if (function_exists('G') && function_exists('param')) {
                $this->addResult('核心函数', true, '核心函数G()和param()可用');
            } else {
                $this->addResult('核心函数', false, '核心函数缺失');
            }
            
        } catch (Exception $e) {
            $this->addResult('框架加载', false, '框架加载异常: ' . $e->getMessage());
        }
    }
    
    private function testStaticResources() {
        echo "3. 测试静态资源...\n";
        
        $resources = [
            '/css/bootstrap.css' => 'Bootstrap CSS',
            '/css/bootstrap-bbs.css' => 'BBS样式',
            '/js/jquery-3.1.0.js' => 'jQuery库',
            '/js/bootstrap.js' => 'Bootstrap JS',
            '/js/boyou.js' => 'Boyou框架JS',
            '/view/img/logo.png' => '网站Logo',
            '/img/favicon.ico' => '网站图标'
        ];
        
        foreach ($resources as $path => $description) {
            $headers = get_headers($this->baseUrl . $path, 1);
            if ($headers && strpos($headers[0], '200') !== false) {
                $this->addResult("资源: $description", true, "正常加载");
            } else {
                $this->addResult("资源: $description", false, "加载失败");
            }
        }
    }
    
    private function testPageRendering() {
        echo "4. 测试页面渲染...\n";
        
        $pages = [
            '/' => '首页',
            '/admin/' => '管理后台',
            '/install/' => '安装页面'
        ];
        
        foreach ($pages as $path => $description) {
            $response = $this->makeRequest($path);
            if ($response !== false) {
                // 检查HTML结构
                if (strpos($response, '<html') !== false && strpos($response, '</html>') !== false) {
                    $this->addResult("页面: $description", true, "HTML结构完整");
                } else {
                    $this->addResult("页面: $description", false, "HTML结构不完整");
                }
            } else {
                $this->addResult("页面: $description", false, "页面无法访问");
            }
        }
    }
    
    private function testDatabaseConnection() {
        echo "5. 测试数据库连接...\n";
        
        // 检查数据库文件
        if (file_exists('data/boyou_bbs.db')) {
            $this->addResult('数据库文件', true, 'SQLite数据库文件存在');
            
            // 尝试连接数据库
            try {
                $pdo = new PDO('sqlite:data/boyou_bbs.db');
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // 测试查询
                $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' LIMIT 5");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (count($tables) > 0) {
                    $this->addResult('数据库连接', true, '数据库连接正常，包含' . count($tables) . '个表');
                } else {
                    $this->addResult('数据库连接', false, '数据库为空或无表');
                }
                
            } catch (PDOException $e) {
                $this->addResult('数据库连接', false, '数据库连接失败: ' . $e->getMessage());
            }
        } else {
            $this->addResult('数据库文件', false, '数据库文件不存在');
        }
    }
    
    private function testConfigurationFiles() {
        echo "6. 测试配置文件...\n";
        
        $configFiles = [
            'conf/conf.php' => '主配置文件',
            'conf/conf.default.php' => '默认配置文件',
            'data/install.lock' => '安装锁文件'
        ];
        
        foreach ($configFiles as $file => $description) {
            if (file_exists($file)) {
                $this->addResult("配置: $description", true, "文件存在");
                
                // 检查配置文件内容
                if ($file === 'conf/conf.php') {
                    $conf = include $file;
                    if (is_array($conf) && isset($conf['sitename'])) {
                        $this->addResult('配置内容', true, '配置文件格式正确，站点名: ' . $conf['sitename']);
                    } else {
                        $this->addResult('配置内容', false, '配置文件格式错误');
                    }
                }
            } else {
                $this->addResult("配置: $description", false, "文件不存在");
            }
        }
    }
    
    private function makeRequest($path) {
        $url = $this->baseUrl . $path;
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET'
            ]
        ]);
        
        return @file_get_contents($url, false, $context);
    }
    
    private function addResult($test, $success, $message) {
        $this->testResults[] = [
            'test' => $test,
            'success' => $success,
            'message' => $message
        ];
        
        $status = $success ? '✓' : '✗';
        echo "  $status $test: $message\n";
    }
    
    private function generateReport() {
        echo "\n=== 测试报告 ===\n";
        
        $total = count($this->testResults);
        $passed = array_filter($this->testResults, function($result) {
            return $result['success'];
        });
        $passedCount = count($passed);
        $failedCount = $total - $passedCount;
        
        echo "总测试数: $total\n";
        echo "通过测试: $passedCount\n";
        echo "失败测试: $failedCount\n";
        echo "成功率: " . round(($passedCount / $total) * 100, 1) . "%\n";
        
        if ($failedCount > 0) {
            echo "\n失败的测试:\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "  ✗ {$result['test']}: {$result['message']}\n";
                }
            }
        }
        
        if ($passedCount === $total) {
            echo "\n🎉 所有测试都通过了！网站功能正常！\n";
        } elseif ($passedCount / $total >= 0.8) {
            echo "\n⚠️  大部分测试通过，有少量问题需要处理\n";
        } else {
            echo "\n❌ 多项测试失败，需要进一步检查\n";
        }
        
        // 保存测试报告
        $reportData = [
            'test_time' => date('Y-m-d H:i:s'),
            'total_tests' => $total,
            'passed_tests' => $passedCount,
            'failed_tests' => $failedCount,
            'success_rate' => round(($passedCount / $total) * 100, 1),
            'results' => $this->testResults
        ];
        
        file_put_contents('website_test_report.json', json_encode($reportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n详细报告已保存到: website_test_report.json\n";
    }
}

// 检查服务器是否运行
function checkServerRunning() {
    $context = stream_context_create([
        'http' => [
            'timeout' => 3,
            'method' => 'GET'
        ]
    ]);
    
    $response = @file_get_contents('http://localhost:8000', false, $context);
    return $response !== false;
}

// 主程序
if (checkServerRunning()) {
    echo "✓ 检测到Web服务器正在运行\n\n";
    $tester = new WebsiteTester();
    $tester->runAllTests();
} else {
    echo "⚠️  Web服务器未运行，正在启动...\n";
    echo "请在另一个终端运行: php -S localhost:8000\n";
    echo "然后重新运行此测试脚本\n";
}

echo "\n=== 网站功能测试完成 ===\n";

?>
